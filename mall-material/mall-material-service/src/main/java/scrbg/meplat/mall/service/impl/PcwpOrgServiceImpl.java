package scrbg.meplat.mall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.entity.PcwpOrg;
import scrbg.meplat.mall.mapper.PcwpOrgMapper;
import scrbg.meplat.mall.service.PcwpOrgService;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PcwpOrgServiceImpl  extends ServiceImpl<PcwpOrgMapper, PcwpOrg> implements PcwpOrgService {
    @Override
    public List<PcwpOrg> getTree(String name) {
        QueryWrapper<PcwpOrg> pcwpOrgQueryWrapper = new QueryWrapper<>();
        if(name != null && !name.isEmpty()){
            pcwpOrgQueryWrapper.like("name", name);
        }
        pcwpOrgQueryWrapper.ne("mdmstate", -1);
        pcwpOrgQueryWrapper.eq("type", "O");// O: 组织，D: 部门
        pcwpOrgQueryWrapper.notIn("orglayertypenumber", "4","8");// 组织层级编码过滤
        pcwpOrgQueryWrapper.orderByAsc("sortcode");

        List<PcwpOrg> pcwpOrgs = baseMapper.selectList(pcwpOrgQueryWrapper);

        // 如果查询结果为空，直接返回空列表
        if (pcwpOrgs == null || pcwpOrgs.isEmpty()) {
            return Collections.emptyList();
        }

        // 过滤出根节点（parentid为null或空的节点）
        List<PcwpOrg> rootNodes = pcwpOrgs.stream()
                .filter(org -> org.getParentid() == null || org.getParentid().isEmpty())
                .collect(Collectors.toList());

        // 如果根节点为空，但查询结果不为空，则将所有节点视为根节点
        if (rootNodes.isEmpty()) {
            rootNodes = new ArrayList<>(pcwpOrgs);
        }

        // 为根节点设置显示名称并构建树形结构
        for (PcwpOrg rootNode : rootNodes) {
            // 为根节点添加组织层级类型名称
            if (rootNode.getName() != null && rootNode.getOrglayertypename() != null) {
                rootNode.setName(rootNode.getName() + "(" + rootNode.getOrglayertypename() + ")");
            }
            buildTree(rootNode, pcwpOrgs);
        }

        return rootNodes;
    }
    @Override
    public List<PcwpOrg> getTree4User(String id,String name) {
        List<PcwpOrg> list = null;
        if(StringUtils.isBlank(name)){
            list =  baseMapper.selectList(new QueryWrapper<PcwpOrg>().eq("parentid", id)
                            .eq("type","O").notIn("orglayertypenumber", "4","8")
                    .ne("mdmstate", -1).orderByAsc("sortcode"));
        }else{
            list = baseMapper.selectList(new QueryWrapper<PcwpOrg>().like("name",name)
                    .eq("type","O").notIn("orglayertypenumber", "4","8")
                            .ne("mdmstate", -1).orderByAsc("sortcode"));
        }
        return list;
    }

    @Override
    public PcwpOrg getBySortCode(String sortCode) {
        if (sortCode == null) {
            return null;
        }

        QueryWrapper<PcwpOrg> pcwpOrgQueryWrapper = new QueryWrapper<>();
        // 先进行数据库查询（可能不区分大小写）
        pcwpOrgQueryWrapper.eq("id", sortCode);
        List<PcwpOrg> results = baseMapper.selectList(pcwpOrgQueryWrapper);

        // 在应用层面进行区分大小写的精确匹配
        if (results != null && !results.isEmpty()) {
            return results.stream()
                    .filter(org -> sortCode.equals(org.getId()))
                    .findFirst()
                    .orElse(null);
        }

        return null;
    }

    @Override
    public PcwpOrg getByOrgNumber(String orgNumber) {
        if (StringUtils.isBlank(orgNumber)) {
            return null;
        }

        QueryWrapper<PcwpOrg> pcwpOrgQueryWrapper = new QueryWrapper<>();
        // 先进行数据库查询（可能不区分大小写）
        pcwpOrgQueryWrapper.eq("number", orgNumber);
        List<PcwpOrg> results = baseMapper.selectList(pcwpOrgQueryWrapper);
        if (results != null && !results.isEmpty()) {
            return results.stream()
                    .filter(org -> orgNumber.equals(org.getNumber()))
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }
    @Override
    public List<String> getSortCodeList(String sortCode) {
        if (sortCode == null || sortCode.isEmpty()) {
            return Collections.emptyList();
        }

        // 查询所有符合条件的组织机构
        QueryWrapper<PcwpOrg> pcwpOrgQueryWrapper = new QueryWrapper<>();
        pcwpOrgQueryWrapper.likeRight("sortcode", sortCode); // 使用 likeRight 确保是子节点
        pcwpOrgQueryWrapper.ne("mdmstate", -1);// 排除已删除的
        pcwpOrgQueryWrapper.eq("issealup", 0);// 未封存
        pcwpOrgQueryWrapper.eq("type", "O");//  组织
        pcwpOrgQueryWrapper.eq("orglayertypenumber", "9");// 组织层级编码为9的项目部，只有项目部才有订单
        List<PcwpOrg> pcwpOrgs = baseMapper.selectList(pcwpOrgQueryWrapper);

        // 如果查询结果为空，返回空列表
        if (pcwpOrgs == null || pcwpOrgs.isEmpty()) {
            return Collections.emptyList();
        }

        // 提取所有 sortcode 并返回
        return pcwpOrgs.stream()
                .map(PcwpOrg::getSortcode)
                .filter(s -> s != null && !s.isEmpty())
                .collect(Collectors.toList());
    }

    @Override
    public List<PcwpOrg> getChildren(String id) {
        return baseMapper.selectList(new QueryWrapper<PcwpOrg>().eq("parentid", id));
    }

    @Override
    public List<PcwpOrg> getTopOrg() {
        return baseMapper.selectList(new QueryWrapper<PcwpOrg>().eq("parentid", ""));
    }

    @Override
    public String getPcwpOrgId(String orgNumber) {
        return baseMapper.getPcwpOrgId(orgNumber);
    }

    /**
     * 递归构建树形结构
     * @param parentNode 父节点
     * @param allOrgs 所有组织列表
     */
    private void buildTree(PcwpOrg parentNode, List<PcwpOrg> allOrgs) {
        List<PcwpOrg> children = allOrgs.stream()
                .filter(org -> org.getParentid() != null && org.getParentid().equals(parentNode.getId()))
                .collect(Collectors.toList());

        if (!children.isEmpty()) {
            parentNode.setChildren(children);
            for (PcwpOrg child : children) {
                // 为子节点添加组织层级类型名称
                if (child.getName() != null && child.getOrglayertypename() != null) {
                    child.setName(child.getName() + "(" + child.getOrglayertypename() + ")");
                }
                buildTree(child, allOrgs);
            }
        }
    }
}
