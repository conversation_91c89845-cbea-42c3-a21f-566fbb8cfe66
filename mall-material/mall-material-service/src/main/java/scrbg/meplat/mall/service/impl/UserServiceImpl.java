package scrbg.meplat.mall.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import lombok.extern.log4j.Log4j2;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;

import io.seata.spring.annotation.GlobalTransactional;
import scrbg.meplat.mall.common.redis.RedisKey;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.auth.RoleEnum;
import scrbg.meplat.mall.dto.DsaffDTO;
import scrbg.meplat.mall.dto.OrganizationDTO;
import scrbg.meplat.mall.dto.outer.login.LoginUser;
import scrbg.meplat.mall.dto.outer.login.ResponseBody;
import scrbg.meplat.mall.dto.outer.login.UserInfo;
import scrbg.meplat.mall.dto.user.MallRole;
import scrbg.meplat.mall.dto.user.OrgAndSon;
import scrbg.meplat.mall.dto.user.userCenter.CreateShopExternalDTO;
import scrbg.meplat.mall.dto.user.userCenter.CreateShopInsideDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.CodeEnum;
import scrbg.meplat.mall.enums.PublicEnum;
import scrbg.meplat.mall.enums.user.AuthArrUtil;
import scrbg.meplat.mall.enums.user.UserEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.EnterpriseInfoMapper;
import scrbg.meplat.mall.mapper.ShopMapper;
import scrbg.meplat.mall.mapper.UserMapper;
import scrbg.meplat.mall.mapper.system.SysUserMapper;
import scrbg.meplat.mall.mapper.system.SysUserRoleMapper;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.auth.model.TokenRes;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.util.ChineseCharacterUtil;
import scrbg.meplat.mall.util.CodeUtil;
import scrbg.meplat.mall.util.CommonUtil;
import scrbg.meplat.mall.util.RegexUtils;
import scrbg.meplat.mall.util.RestTemplateUtils;
import scrbg.meplat.mall.util.SpringBeanUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.user.LoginVO;
import scrbg.meplat.mall.vo.user.OrganizationVO;
import scrbg.meplat.mall.vo.user.UserShopEnterpriseInfoVO;
import scrbg.meplat.mall.vo.user.userCenter.CreateInsideShopEchoInfoVO;
import scrbg.meplat.mall.vo.user.userCenter.CreateShopEchoInfoVO;
import scrbg.meplat.mall.vo.user.userCenter.EnterpriseAuthUpdateVO;
import scrbg.meplat.mall.vo.user.userCenter.ShopStateVO;
import scrbg.meplat.mall.vo.user.userCenter.UserNameAndImgVO;


/**
 * @描述：用户 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
@Log4j2
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    UserService userService;
    @Autowired
    ShopService shopService;

    @Autowired
    ShopSupplierReleService shopSupplierReleService;

    @Autowired
    SysRoleService sysRoleService;

    @Autowired
    SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    UserActivityService userActivityService;

    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    private RestTemplate restTemplate;


    @Autowired
    FileService fileService;

    @Autowired
    private EnterpriseInfoMapper enterpriseInfoMapper;

    @Autowired
    private ShopMapper shopMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private MallConfig mallConfig;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    private RestTemplateUtils restTemplateUtils;

    @Autowired
    private PcwpService pcwpService;
    @Autowired
    private EnterprisePerformanceService enterprisePerformanceService;

    @Autowired
    private Environment env;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<User> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String account = (String) innerMap.get("account");
        String realName = (String) innerMap.get("realName");
        String nickName = (String) innerMap.get("nickName");
        String userMobile = (String) innerMap.get("userMobile");
        String email = (String) innerMap.get("email");
        Integer state = (Integer) innerMap.get("state");
        Integer userType = (Integer) innerMap.get("userType");
        Integer mallType = (Integer) innerMap.get("mallType");
        String enterpriseId = (String) innerMap.get("enterpriseId");
        String keywords = (String) innerMap.get("keywords");

        if (userType != null) {
            queryWrapper.eq(User::getIsInternalUser, userType);
        }
        if (!StringUtils.isEmpty(account)) {
            queryWrapper.eq(User::getAccount, account);
        }
        if (!StringUtils.isEmpty(realName)) {
            queryWrapper.like(User::getRealName, realName);
        }
        if (!StringUtils.isEmpty(nickName)) {
            queryWrapper.like(User::getNickName, nickName);
        }
        if (!StringUtils.isEmpty(userMobile)) {
            queryWrapper.eq(User::getUserMobile, userMobile);
        }
        if (!StringUtils.isEmpty(email)) {
            queryWrapper.like(User::getEmail, email);
        }
        if (state != null) {
            queryWrapper.eq(User::getState, state);
        }
        if (!StringUtils.isEmpty(enterpriseId)) {
            queryWrapper.eq(User::getEnterpriseId, enterpriseId);
        }
        if (!StringUtils.isEmpty(keywords)) {
            if (!StringUtils.isEmpty(keywords)) {
                queryWrapper.and(i -> i.like(User::getUserNumber, keywords)
                        .or().like(User::getNickName, keywords)
                        .or().like(User::getUserMobile, keywords)
                        .or().like(User::getRealName, keywords));
            }
        }
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        if (mallType == 1) {
            queryWrapper.eq(User::getIsDevice, 1);
        }
        if (mallType == 0) {
            queryWrapper.eq(User::getIsMaterial, 1);
        }
        IPage<User> page = this.page(
                new Query<User>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    @Override
    public void create(User user) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(user);
    }

    @Override
    public void update(User user) {
        if (StringUtils.isEmpty(user.getUserId())) {
            UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
            user.setUserId(currentUser.getUserId());
        }
        super.updateById(user);
    }


    @Override
    public User getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }

    /**
     * 通过账号密码登录
     *
     * @param user
     * @return
     */
    @Override
    public User loginByAccount(User user) {
        //验证账号密码是否为空
        if (!user.getAccount().isEmpty() && !user.getPassword().isEmpty()) {
            QueryWrapper<User> wrapper;
            wrapper = new QueryWrapper<>();
            wrapper.eq("account", user.getAccount());
            //验证账号密码是否正确
            User one = getOne(wrapper);
            if (one.getPassword().equals(user.getPassword())) {
                return one;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public R loginByMobile(User user) {

        QueryWrapper<User> wrapper;
        wrapper = new QueryWrapper<>();
        wrapper.eq("user_mobile", user.getUserMobile());
        User one = getOne(wrapper);
        if (one.getPassword().equals(user.getPassword())) {
            return R.success(one, "登录成功！");
        } else {
            return R.failed("密码错误！");
        }
    }
    @Value("${app.verify-code: true}")
    private boolean verifyCode;

    /**
     * 个人用户注册（外部）
     *
     * @param user
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R registration(User user) {
//        if(PasswordUtils.password(user.getPassword())) {
//            throw new BusinessException("密码中必须包含字母、数字、特称字符，至少8个字符，最多20个字符");
//        }
        Integer mallType = user.getMallType();
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        String code = user.getVerificationCode();
        String phone = user.getUserMobile();
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_500200.getCode(), "手机号码格式错误！");
        }
        if (verifyCode) {
            if (org.apache.commons.lang.StringUtils.isBlank(code)) {
                throw new BusinessException(PublicEnum.RESULT_CODE_500200.getCode(), "请输入验证码！");
            }
            String rCode = stringRedisTemplate.opsForValue().get(RedisKey.REGISTER_CODE_KEY + phone);
            if (rCode == null) {
                throw new BusinessException(PublicEnum.RESULT_CODE_500200.getCode(), "验证码已失效，请重新发送！");
            }
            if (!code.equals(rCode.split("_")[0])) {
                throw new BusinessException(PublicEnum.RESULT_CODE_500200.getCode(), "验证码错误！");
            }
        }
        //先查询用户是否存在
        User oneUser = userService.lambdaQuery()
                .eq(User::getUserMobile, user.getUserMobile()).one();
        if (oneUser != null) {
            if (oneUser.getIsMaterial() != null && oneUser.getIsMaterial() == 1) {
                return R.failed("手机号已在物资商城注册！可直接登录！");
            } else if (oneUser.getIsDevice() != null && oneUser.getIsDevice() == 1) {
                return R.failed("手机号已在装备商城注册！可直接登录！");
            } else {
                return R.failed("手机号已注册！可直接登录！");
            }
        } else {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            HashMap<String, Object> map = new HashMap<>();
            HashMap<String, String> userMap = new HashMap<>();
            HashMap<String, Object> orgMap = new HashMap<>();
            userMap.put("phoneNo", phone);
            userMap.put("userName", phone);
            userMap.put("password", user.getPassword());
            //	机构类型(1:个人|2:个体户|3:企业)
            orgMap.put("orgType", 1);
            orgMap.put("orgName", phone);
            map.put("user", userMap);
            map.put("org", orgMap);
            map.put("sysCode", "msp");
            String content = JSON.toJSONString(map);
            headers.add("sysCode", "msp");
            HttpEntity<String> request = new HttpEntity<>(content, headers);
            try {
                // 调用远程进行注册
                String url = mallConfig.prodPcwp2Url + "/identity/auth/userorg/signup";
                R<Map> r = restTemplate.postForObject(url, request, R.class);
                if (r.getCode() == 200) {
                    EnterpriseInfo enterpriseInfo = new EnterpriseInfo();
                    // 企业编号
                    enterpriseInfo.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                    // 哪个商城企业
                    if (mallType == PublicEnum.MATERIALS.getCode()) {
                        enterpriseInfo.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                    } else {
                        enterpriseInfo.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                    }
                    enterpriseInfo.setEnterpriseType(2);
                    enterpriseInfo.setAdminPhone(user.getUserMobile());
                    // 营业状态
                    enterpriseInfo.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                    enterpriseInfo.setIsSupplier(PublicEnum.IS_NO.getCode());
                    // 状态
                    enterpriseInfo.setState(PublicEnum.IS_YES.getCode());
                    // 办理状态
                    enterpriseInfo.setHandlingResult(PublicEnum.IS_YES.getCode());
                    //存入企业附加信息
                    enterpriseInfoService.create(enterpriseInfo);
                    user.setNickName(phone);
                    // TODO  外部用户内部id是否页面使用
                    user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                    user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                    user.setAccount(user.getUserMobile());
                    user.setPassword(user.getPassword());
                    // 状态
                    user.setIsInternalUser(PublicEnum.IS_NO.getCode());
                    user.setState(PublicEnum.IS_YES.getCode());
                    if (mallType == PublicEnum.MATERIALS.getCode()) {
                        user.setIsMaterial(PublicEnum.IS_YES.getCode());
                    } else {
                        user.setIsDevice(PublicEnum.IS_YES.getCode());
                    }
                    boolean save = super.save(user);
                    if (save) {
                        return R.success();
                    } else {
                        return R.failed("注册失败！");
                    }
                } else {
                    throw new BusinessException(r.getCode(), r.getMessage());
                }
            } catch (Exception e) {
                throw new BusinessException(400, e.getMessage());
            }
        }
    }



    @Autowired
    public SystemParamService systemParamService;
    @Override
    public Boolean verifyUserInfo() {
//        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
//        String shopId = ThreadLocalUtil.getCurrentUser().getShopId();
//        String shopName = ThreadLocalUtil.getCurrentUser().getShopName();
//        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
//        Integer isInternalShop = ThreadLocalUtil.getCurrentUser().getIsInternalShop();
//        Integer isInternalSettlement = ThreadLocalUtil.getCurrentUser().getIsInternalSettlement();
//        QueryWrapper<Shop> wrapper = new QueryWrapper<>();
//        wrapper.eq("shop_id", shopId)
//                .eq("enterprise_id", enterpriseId)
//                .eq("shop_name", shopName)
//                .eq("is_internal_shop", isInternalShop)
//                .eq("is_internal_settlement", isInternalSettlement);
//        Shop one = shopService.getOne(wrapper);
//        if (one != null) {
//            User user = getById(userId);
//            return enterpriseId.equals(user.getEnterpriseId());
//        } else {
        return false;
//        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByPublish(List<String> ids, String type) {
        List<User> resutls = listByIds(ids);
        for (User user : resutls) {
            if ("0".equals(type)) {
                user.setState(UserEnum.STATE_START.getCode());
            }
            if ("1".equals(type)) {
                user.setState(UserEnum.STATE_OPEN.getCode());
            }

        }
        super.saveOrUpdateBatch(resutls);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginVO login(String account, String password, Integer mallType, HttpServletRequest request) {
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        Integer userCount = userService.lambdaQuery()
                .and((t) -> t.eq(User::getAccount, account)
                        .or()
                        .eq(User::getUserMobile, account))
                .eq(mallType == 0, User::getMaterialState, 0)
                .eq(mallType == 1, User::getDeviceState, 0).count();
        if (userCount > 0) {
            throw new BusinessException(400, "该用户已被停用，禁止登陆！");
        }
        boolean isPhone = false;
        boolean isHaveInteriorId = false;
        // 校验是不是手机号
        // 注意这里没有密码才可能是手机号码登录，这里的手机号码登录是否是验证码登录？
        if (!RegexUtils.isPhoneInvalid(account) && password == null) {
            isPhone = true;
        }
        R<Map> r = null;
        HttpHeaders thisHeaders = new HttpHeaders();
        thisHeaders.add("sysCode", "msp");
        HashMap<String, Object> thisMap = new HashMap<>();
        thisMap.put("sysCode", "msp");
        String content = JSON.toJSONString(thisMap);
        HttpEntity<String> thisRequest = new HttpEntity<>(content, thisHeaders);
        if (isPhone) {
            // 如果是手机号
            // 如果当前错误次数大于等于8次拒绝登录
            if (password != null) {
                // 使用密码登录
                User userPwd = lambdaQuery().eq(User::getUserMobile, account).one();
                if (userPwd != null && userPwd.getLockedState() == 1) {
                    throw new BusinessException("用户已锁定，请联系管理员！！");
                }
                if (userPwd != null && userPwd.getState() == 0) {
                    throw new BusinessException("用户已被禁用，请联系管理员！！");
                }
            }
            // 这里只是一个手机号就获取了token，是用于验证码登录的场景？
            String url = mallConfig.prodPcwp2Url + "/identity/auth/createExternalToken?phoneNo=" + account + "&sysCode=msp";
            try {
                r = restTemplate.postForObject(url, thisRequest, R.class);
            } catch (Exception e) {
                log.info(e.getMessage());
                throw new BusinessException("【远程异常】手机号登录异常请联系管理员！"+e.getMessage());
            }
        } else {
            String url = mallConfig.prodPcwp2Url + "/identity/auth/signin?account="
                    + account + "&password=" + password + "&identityType=5&sysCode=msp";
            try {
                r = restTemplate.postForObject(url, null, R.class);
            } catch (Exception e) {
                log.info(e.getMessage());
                throw new BusinessException("【远程异常】账户登录异常请联系管理员！"+e.getMessage());
            }
        }
        if (r.getCode() == 200) {
            LoginVO vo = BeanUtils.mapToBean(r.getData(), LoginVO.class);
            vo.setOriginalUserName(vo.getUserName());
            vo.setFarUserId(vo.getUserId());
            vo.setUserNumber(vo.getUserNumber());
            Integer isInterior = 0;
            if (vo.getIsExternal() == null) {
                vo.setIsExternal(1);
                isInterior = 0;
            }
            if (vo.getIsExternal() == 0) {//是否外部用户，外部用户和内部用户互斥关系
                isInterior = 1;
            }
            vo.setIsInterior(isInterior);
            // 是内部用户
            if (isInterior == 1) {
                if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId())) {
                    LoginVO loginVO = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId()), LoginVO.class);
                    log.warn("已登陆，直接登陆成功！");
                    return loginVO;
                }
                // 内部都是企业
                vo.setEnterpriseType(1);
                User qUser = userService.lambdaQuery().eq(User::getInteriorId, vo.getUserId()).one();
                // 机构暂时使用正式
                String orgUrl = mallConfig.prodPcwp2Url + "/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
                R orgR = null;
                try {
                    orgR = restTemplate.getForObject(orgUrl, R.class);
                } catch (Exception e) {
                    throw new BusinessException("【远程异常】" + e.getMessage());
                }
                if (orgR.getCode() == 200) {
                    ArrayList orgArr = (ArrayList) orgR.getData();
                    // 第一个就是当前企业
                    if (!CollectionUtils.isEmpty(orgArr)) {
                        Map orgMap = (Map) orgArr.get(0);
                        if(env.containsProperty("mall.isYueshan") && "030138".equals(account)){//如果是岳山，就倒序，防止大家老是切换机构挤掉
                            orgMap = (Map) orgArr.get(orgArr.size()-1);
                        }
                        log.info("当前机构：" + orgMap);
                        vo.setOrgInfo(orgMap);
                        // 设置当前机构
                        String orgId = (String) orgMap.get("orgId");
                        String orgName = (String) orgMap.get("orgName");
                        String orgNumber = (String) orgMap.get("shortCode");
                        vo.setOrgId(orgId);
                        vo.setOrgName(orgName);
                        vo.setOrgNumber(orgNumber);
                        HttpHeaders headers = new HttpHeaders();
                        headers.add("token", vo.getToken());
                        headers.add("org", JSONObject.toJSONString(orgMap));
                        log.info("当前机构JSON：" + JSONObject.toJSONString(orgMap));
                        /*if (mallType == 1) {
                            headers.add("sysCode", "egp");
                        }
                        if (mallType == 0) {
                            headers.add("sysCode", "msp");
                        }*/
                        headers.add("sysCode", "msp");//不判断物资还是装备了
//                        System.out.println("当前机构的json：" + JSONObject.toJSONString(orgMap));
                        // 配置角色数据
                        configRoteInfo(mallType, vo, headers);
                        // 本机构以及其下机构列表
                        String orgCUrl = mallConfig.prodPcwp2Url + "/hr/org/getChildrenOrg?orgId=" + orgId
                                + "&orgName=" + orgName;
                        ResponseEntity<R> mapResponseEntity = restTemplateUtils.get(orgCUrl, headers, R.class);
                        R thisAndChild = mapResponseEntity.getBody();
                        if (thisAndChild.getCode() == 200) {
                            ArrayList childList = (ArrayList) thisAndChild.getData();
                            ArrayList<String> orgIds = new ArrayList<>();
                            ArrayList<OrgAndSon> orgAndSons = new ArrayList<>();
                            if (!CollectionUtils.isEmpty(childList)) {
                                for (Object o : childList) {
                                    Map ch = (Map) o;
                                    orgIds.add((String) ch.get("orgId"));
                                    OrgAndSon orgAndSon = new OrgAndSon();
                                    orgAndSon.setOrgId((String) ch.get("orgId"));
                                    orgAndSon.setOrgName((String) ch.get("orgName"));
                                    orgAndSon.setShortCode((String) ch.get("shortCode"));
                                    orgAndSons.add(orgAndSon);
                                }
                            }
                            // 当前机构id以及子机构id
                            vo.setOrgIds(orgIds);
                            vo.setOrgAndSon(orgAndSons);
                        }
//                        System.out.println("查询当前机构以及子机构：" + mapResponseEntity.getBody());
                        // 设置机构列表
                        ArrayList<OrganizationVO> organizationVOS = new ArrayList<>();
                        boolean orgFlag = false;
                        for (Object o : orgArr) {
                            Map map = (Map) o;
                            OrganizationVO organizationVO = new OrganizationVO();
                            organizationVO.setOrgId((String) map.get("orgId"));
                            organizationVO.setOrgName((String) map.get("orgName"));
                            organizationVO.setShortCode((String) map.get("shortCode"));
                            organizationVO.setOrgType((Integer) map.get("orgType"));
                            // 物资
                            if (mallType == 0) {
                                if (mallConfig.isBusinessOrg==1){
                                    Boolean flag = systemParamService.getIsBusinessOrg(organizationVO.getOrgId());
                                    if (flag) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }else {
                                    if (mallConfig.isMPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }

                            }
                            // 装备
                            if (mallType == 1) {
                                if (mallConfig.isDPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                    orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                }
                            }
                            organizationVOS.add(organizationVO);
                        }

                        if (orgFlag) {
                            vo.setIsPlatformAdmin(1);
                        } else {
                            vo.setIsPlatformAdmin(0);
                        }
                        // 特殊处理账号权限
                        List<String> authArr = new ArrayList<>();
                        AuthArrUtil.addAuth(authArr);
                        if (authArr.contains(account)){
                            vo.setIsPlatformAdmin(1);
                        }
                        vo.setOrganizationVOS(organizationVOS);
                    }
                } else {
                    throw new BusinessException(orgR.getCode(), orgR.getMessage());
                }
                if (qUser == null) {
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    if (enterpriseInfo == null) {
                        // 企业不存在
                        EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                        // 企业编号
                        enterpriseInfo1.setCreationTime(new Date());
                        enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                        // 注意这里interiorId赋值了，只有tt系统原来就能登录的用户，interiorId才会有值
                        enterpriseInfo1.setInteriorId(vo.getOrgId());
                        enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                        enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                        enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                        } else {
                            enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                        }
                        enterpriseInfo1.setEnterpriseType(1);
                        enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                        int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                        if (insert != 0) {
                            vo.setLocalOrgId(enterpriseInfo1.getEnterpriseId());
                            vo.setEnterpriseName(enterpriseInfo1.getEnterpriseName());
                            vo.setEnterpriseType(enterpriseInfo1.getEnterpriseType());
                            // 如果用户不存在则保存内部用户数据
                            User user = new User();
                            if (isPhone) {
                                user.setUserMobile(account);
                            } else {
                                user.setAccount(account);
                            }
                            if (!isPhone) {
                                user.setPassword(password);
                            }
                            user.setInteriorId(vo.getUserId());
                            user.setNickName(vo.getUserName());
                            user.setEnterpriseId(enterpriseInfo1.getEnterpriseId());
                            user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                            // 状态
                            user.setIsInternalUser(isInterior);
                            user.setState(PublicEnum.IS_YES.getCode());
                            if (mallType == PublicEnum.MATERIALS.getCode()) {
                                user.setIsMaterial(PublicEnum.IS_YES.getCode());
                            } else {
                                user.setIsDevice(PublicEnum.IS_YES.getCode());
                            }
                            boolean save = userService.save(user);
                            if (save) {
                                vo.setUserId(user.getUserId());
                                vo.setUserName(user.getNickName());
                            }
                        }
                    } else {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        // 企业存在用户不存在
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 如果用户不存在则保存内部用户数据
                        User user = new User();
                        if (isPhone) {
                            user.setUserMobile(account);
                        } else {
                            user.setAccount(account);
                        }
                        if (!isPhone) {
                            user.setPassword(password);
                        }
                        user.setInteriorId(vo.getUserId());
                        user.setNickName(vo.getUserName());
                        user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                        user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                        user.setIsInternalUser(isInterior);
                        user.setState(PublicEnum.IS_YES.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            user.setIsMaterial(PublicEnum.IS_YES.getCode());
                        } else {
                            user.setIsDevice(PublicEnum.IS_YES.getCode());
                        }
                        boolean save = userService.save(user);
                        if (save) {
                            vo.setUserId(user.getUserId());
                            vo.setUserName(user.getNickName());
                        }
                    }
                } else {
                    userIsEnable(vo.getUserNumber());
                    // 用户存在
                    vo.setUserId(qUser.getUserId());
                    vo.setUserName(qUser.getNickName());
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();

                    // TODO 用户存在但是企业不存在
                    if(enterpriseInfo == null){
                        // 企业不存在
                        EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                        // 企业编号
                        enterpriseInfo1.setCreationTime(new Date());
                        enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                        enterpriseInfo1.setInteriorId(vo.getOrgId());
                        enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                        enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                        enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                        } else {
                            enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                        }
                        enterpriseInfo1.setEnterpriseType(1);
                        enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                        int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                        if (insert!=0){
                            enterpriseInfo = enterpriseInfo1;
                        }
                    }
                    // 企业名称变更
                    if (!enterpriseInfo.getEnterpriseName().equals(vo.getOrgName())){
                        enterpriseInfo.setEnterpriseName(vo.getOrgName());
                        enterpriseInfoService.updateById(enterpriseInfo);
                    }
                    if (enterpriseInfo != null) {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 每次登陆修改回当前机构
                        LambdaUpdateChainWrapper<User> setL = userService.lambdaUpdate().eq(User::getUserId, qUser.getUserId())
                                .set(User::getEnterpriseId, enterpriseInfo.getEnterpriseId());
                        // 从token 登陆进来可能不会保存用户账号，如果这里用户但是账号或手机号不存在则需要保存
                        String account1 = qUser.getAccount();
                        String userMobile = qUser.getUserMobile();
                        if (StringUtils.isEmpty(account1) && StringUtils.isEmpty(userMobile)) {
                            // 如果账号手机号都不存在则保存
                            setL.set(isPhone, User::getUserMobile, account);
                            setL.set(!isPhone, User::getAccount, account);
                        }
                        setL.update();
                        if (vo.getIsShpAuthority() == 1) {
                            Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .eq(Shop::getMallType, mallType)
                                    .one();
                            if (shop != null) {
                                vo.setShopId(shop.getShopId());
                                vo.setShopName(shop.getShopName());
                                vo.setShopType(shop.getShopType());
                            }
                        }
                    }
                }
            } else {
                vo.setIsSubmitOrder(1);
                vo.setIsCheck(0);
                vo.setOrgNumber("0");
                vo.setIsPlatformAdmin(0);
                vo.setIsTender(0);
                // 外部用户
                // 外部用户都是手机号登陆
                User userOne = lambdaQuery()
//                        .eq(!isPhone,User::getAccount, account)
                        .eq(User::getUserMobile, account)
                        .eq(User::getIsInternalUser, 0)
                        .one();
                // 用户存在
                if (userOne != null) {
                    if(0 == userOne.getState()){
                        throw new BusinessException(400, "账号被锁定无法登陆！");
                    }
                    isHaveInteriorId = true;//强行为true即可，一直去更新那个字段值保持最新即可
                    vo.setUserId(userOne.getUserId());
                    vo.setUserNumber(userOne.getUserNumber());
                    vo.setUserName(userOne.getNickName());
                    vo.setUserMobile(userOne.getUserMobile());
                    vo.setFarUserId(userOne.getInteriorId());
                    // 都有电商权限
                    vo.setIsShpAuthority(1);
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getEnterpriseId, userOne.getEnterpriseId()).one();
                    if (enterpriseInfo != null) {
//                        if (enterpriseInfo.getIsSupplier() == 0 && enterpriseInfo.getIsNoSupplierAudit() == 1) {
//                            throw new BusinessException(400, "账号未通过供应商审核，账号被锁定无法登陆！");
//                        }
                        vo.setSocialCreditCode(enterpriseInfo.getSocialCreditCode());
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2 && enterpriseInfo.getIsNoSupplierAudit() != 1) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        vo.setIsPcwp(enterpriseInfo.getIsPcwp());
                        vo.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                        vo.setZcstate(enterpriseInfo.getZcstate());
                        if (vo.getIsShpAuthority() == 1) {
                            if (mallType == 0) {
                                Shop shopDate = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                        .one();
                                if (shopDate != null) {
                                    vo.setShopId(shopDate.getShopId());
                                    vo.setShopType(shopDate.getShopType());
                                    vo.setShopName(shopDate.getShopName());
                                }
                            }
                            //这块代码可以去掉了，这是装备
                            /*if (mallType == 1) {
                                Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                        .eq(Shop::getMallType, mallType)
                                        .one();
                                if (shop != null) {
                                    vo.setShopId(shop.getShopId());
                                    vo.setShopType(shop.getShopType());
                                    vo.setShopName(shop.getShopName());
                                    if (mallType == 1) {
                                        vo.setIsOtherAuth(JSONObject.parseObject(shop.getIsOtherAuth(), Map.class));
                                    }
                                }
                            }*/
                        }
//                        Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
//                                .eq(Shop::getMallType, mallType)
//                                .one();
//                        if (shop != null) {
//                            vo.setShopId(shop.getShopId());
//                            vo.setShopType(shop.getShopType());
//                            if (mallType == 1) {
//                                vo.setIsOtherAuth(JSONObject.parseObject(shop.getIsOtherAuth(), Map.class));
//                            }
//                        }
                    }
                } else {
                    throw new BusinessException(400, "用户信息不存在！");
                }
            }
//            stringRedisTemplate.opsForValue()
//                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            boolean material = false;
            boolean device = false;
            // 修改
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                material = true;
            } else {
                device = true;
            }
            // 内部用户修改
            LambdaUpdateChainWrapper<User> eq = userService.lambdaUpdate()
                    .eq(User::getUserId, vo.getUserId());
            eq.set(isHaveInteriorId, User::getInteriorId, vo.getFarUserId());
            eq.set(material, User::getIsMaterial, PublicEnum.IS_YES.getCode())
                    .set(device, User::getIsDevice, PublicEnum.IS_YES.getCode())
                    .set(!StringUtils.isEmpty(password), User::getPassword, password)
                    .setSql("login_count = login_count + 1")
                    .set(User::getGmtLogin, new Date()).update();



            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);


            if (vo.getOrgInfo() != null) {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(EnterpriseInfo::getShortCode, vo.getOrgInfo().get("shortCode"))
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            } else {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            }
            if (password == null) {
                userActivityM(vo, "手机号登陆", request);
            } else {
                userActivityM(vo, "账号密码登陆", request);
            }
            return vo;
        } else {
            // 密码错误
            if (r.getMessage().equals("密码错误")) {
                UserService service = SpringBeanUtil.getBean(UserService.class);
                service.handlePwdError(account);
            }
            throw new BusinessException(r.getCode(), r.getMessage());
        }

    }

    private boolean isOrgFlag(Integer mallType, LoginVO vo, OrganizationVO organizationVO) {
        boolean orgFlag = false;
        String isShopAuthStr = null;
        if (mallType == 1) {
            isShopAuthStr = "装备采购平台-商城";
        }
        if (mallType == 0) {
            isShopAuthStr = "物资采购平台-商城";
        }
        HttpHeaders headers2 = new HttpHeaders();
        if (mallType == 1) {
            headers2.add("sysCode", "egp");
        }
        if (mallType == 0) {
            headers2.add("sysCode", "msp");
        }
        headers2.add("token", vo.getToken());
        headers2.add("org", JSONObject.toJSONString(organizationVO));
        // 获取角色列表
        String rotes = mallConfig.prodPcwp2Url + "/hr/role/getUserHasRoles?orgId=" + organizationVO.getOrgId() + "&userId=" + vo.getFarUserId();
        R rotesR = null;
        try {
            rotesR = restTemplateUtils.get(rotes, headers2, R.class).getBody();
        } catch (Exception e) {
            log.error("角色接口异常！" + e.getMessage());
            throw new BusinessException("角色权限远程接口异常！");
        }
        log.warn("自营机构角色信息：" + rotesR);
        if (rotesR.getCode() != null && rotesR.getCode() == 200) {
            ArrayList rotesList = (ArrayList) rotesR.getData();
            // 是否有电商管理权限
            boolean isShopAuthStrB = rotesList.contains(isShopAuthStr);
            if (isShopAuthStrB) {
                orgFlag = true;
            }
        } else {
            log.error("角色接口异常！" + rotesR);
            throw new BusinessException(500, "角色权限远程接口异常！");
        }
        return orgFlag;
    }

    private void configRoteInfo(Integer mallType, LoginVO vo, HttpHeaders headers) {
        String isShopAuthStr = null;
        String isCheckStr = null;
        String isDeviceRecycle = null;
        String isMaterialFlow = null;
        String isSubmitOrder = null;
        String isMonthPlanAudit = null;
        if (mallType == 0) {
            isShopAuthStr = "物资采购平台-商城";
            isCheckStr = "物资采购平台-纪检";
            isSubmitOrder = "物资下单权限";
            isMonthPlanAudit = "物资月供计划审核";
        }
        // 获取角色列表
        String rotes = mallConfig.prodPcwp2Url + "/hr/role/getUserHasRoles?orgId=" + vo.getOrgId() + "&userId=" + vo.getFarUserId();
        R rotesR = null;
        try {
            rotesR = restTemplateUtils.get(rotes, headers, R.class).getBody();
        } catch (Exception e) {
            log.error("角色接口异常！" + e.getMessage());
            throw new BusinessException("角色权限远程接口异常！");
        }
        if (rotesR.getCode() != null && rotesR.getCode() == 200) {
            ArrayList rotesList = (ArrayList) rotesR.getData();
            vo.setRoles(rotesList);
            // 是否有月供审核权限
            boolean isMonthPlanAuditB = rotesList.contains(isMonthPlanAudit);
            if (isMonthPlanAuditB) {
                vo.setIsMonthPlanAudit(1);
            } else {
                vo.setIsMonthPlanAudit(0);
            }
            // 是否有下单权限
            boolean isSubmitOrderB = rotesList.contains(isSubmitOrder);
            if (isSubmitOrderB) {
                vo.setIsSubmitOrder(1);
            } else {
                vo.setIsSubmitOrder(0);
            }
            // 是否有电商管理权限
            boolean isShopAuthStrB = rotesList.contains(isShopAuthStr);
            if (isShopAuthStrB) {
                vo.setIsShpAuthority(1);
            } else {
                vo.setIsShpAuthority(0);
            }
            // 是否有纪检权限
            boolean isCheckStrB = rotesList.contains(isCheckStr);
            isCheckStrB = false;//暂时不需要这个纪检了
            if (isCheckStrB) {
                vo.setIsCheck(1);
            } else {
                vo.setIsCheck(0);
            }
        } else {
            log.error("角色接口异常！" + rotesR);
            throw new BusinessException(500, "角色权限远程接口异常！");
        }

        /*if (rotesR.getCode() != null && rotesR.getCode() == 200) {
            ArrayList rotesList = (ArrayList) rotesR.getData();
            vo.setRoles(rotesList);
            // 是否有物流运营中心权限
            boolean isMaterialFlowB = rotesList.contains(isMaterialFlow);
            if (isMaterialFlowB) {
                vo.setIsMaterialFlow(1);
            } else {
                vo.setIsMaterialFlow(0);
            }
            // 是否有二手设备回收运营中心权限
            boolean isDeviceRecycleB = rotesList.contains(isDeviceRecycle);
            if (isDeviceRecycleB) {
                vo.setIsDeviceRecycle(1);
            } else {
                vo.setIsDeviceRecycle(0);
            }
            // 是否有月供审核权限
            boolean isMonthPlanAuditB = rotesList.contains(isMonthPlanAudit);
            if (isMonthPlanAuditB) {
                vo.setIsMonthPlanAudit(1);
            } else {
                vo.setIsMonthPlanAudit(0);
            }
            // 是否有下单权限
            boolean isSubmitOrderB = rotesList.contains(isSubmitOrder);
            if (isSubmitOrderB) {
                vo.setIsSubmitOrder(1);
            } else {
                vo.setIsSubmitOrder(0);
            }
            // 是否有电商管理权限
            boolean isShopAuthStrB = rotesList.contains(isShopAuthStr);
            if (isShopAuthStrB) {
                vo.setIsShpAuthority(1);
            } else {
                vo.setIsShpAuthority(0);
            }
            // 是否有纪检权限
            boolean isCheckStrB = rotesList.contains(isCheckStr);
            if (isCheckStrB) {
                vo.setIsCheck(1);
            } else {
                vo.setIsCheck(0);
            }
        } else {
            log.error("角色接口异常！" + rotesR);
            throw new BusinessException(500, "角色权限远程接口异常！");
        }*/
        // 获取九宫格菜单用户查询是否有招标权限
        String menuUrl = mallConfig.prodPcwp2Url + "/permission/menu/getSpeedDial";
        R menuR = restTemplateUtils.get(menuUrl, headers, R.class).getBody();
        String menuS = menuR.toString();
        if (menuS.indexOf("1436221334647472128") != -1) {
            vo.setIsTender(1);
        } else {
            vo.setIsTender(0);
        }
    }

    /**
     * 开店回显数据
     *
     * @return
     */
    @Override
    public CreateShopEchoInfoVO createShopEchoInfo(Integer mallType) {
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        CreateShopEchoInfoVO vo = new CreateShopEchoInfoVO();
        User user = getById(userId);
        if (user != null) {
            // 查询企业
            LambdaQueryChainWrapper<EnterpriseInfo> q = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId());
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                q.eq(EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode());
            } else {
                q.eq(EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode());
            }
            EnterpriseInfo enterpriseInfo = q.one();
            if (enterpriseInfo != null) {
                Integer enterpriseType = enterpriseInfo.getEnterpriseType();
                vo.setShopType(enterpriseType);
                // 回显赋值
                vo.setIdentityCardFace(enterpriseInfo.getCardPortraitFace());
                vo.setIdentityCardFaceId(enterpriseInfo.getCardPortraitFaceId());
                vo.setIdentityCardBadge(enterpriseInfo.getCardPortraitNationalEmblem());
                vo.setIdentityCardBadgeId(enterpriseInfo.getCardPortraitNationalEmblemId());
                vo.setRealName(enterpriseInfo.getAdminName());
                vo.setIdentityCard(enterpriseInfo.getAdminNumber());
            }
        }
        return vo;
    }

    /**
     * 开店外部
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createShopExternal(CreateShopExternalDTO dto) {
        Integer mallType = mallConfig.mallType;
        if (!StringUtils.isEmpty(dto.getShopId())) {
            List<Shop> list = shopService.lambdaQuery().eq(Shop::getShopName, dto.getShopName())
                    .eq(Shop::getMallType, mallType)
                    .select(Shop::getShopId).list();
            if (!CollectionUtils.isEmpty(list) && !(list.get(0).getShopId().equals(dto.getShopId()))) {
                throw new BusinessException(400, "店铺名称重复");
            }
            Shop shop = new Shop();
            shop.setShopId(dto.getShopId());
            shop.setAuditStatus(2);
            shop.setGmtModified(new Date());
            shop.setShopName(dto.getShopName());
            String oneChar = ChineseCharacterUtil.convertHanzi2Pinyin(shop.getShopName().substring(0, 1), false);
            shop.setInitial(oneChar.toUpperCase());
            shop.setProvince(dto.getProvince());
            shop.setCity(dto.getCity());
            shop.setCounty(dto.getCounty());
            shop.setDetailedAddress(dto.getDetailedAddress());
            shop.setMainBusiness(dto.getMainBusiness());
            shop.setIsOtherAuth(JSON.toJSONString(dto.getIsOtherAuth()));
            shopService.update(shop);
            fileService.deleteBatchFileByRelevanceIdAndType(dto.getShopId(), 4);
            fileService.deleteBatchFileByRelevanceIdAndType(dto.getShopId(), 9);
            if (!CollectionUtils.isEmpty(dto.getFiles())) {
                List<File> files = dto.getFiles();
                for (File file : files) {
                    file.setRelevanceId(shop.getShopId());
                }
                boolean b = fileService.saveBatch(files);
                if (!b) {
                    throw new BusinessException(400, "附件资料保存失败！");
                }
            }
            return;
        }
        Integer count1 = shopService.lambdaQuery().eq(Shop::getShopName, dto.getShopName())
                .eq(Shop::getMallType, mallType).count();
        if (count1 > 0) {
            throw new BusinessException(400, "店铺名称重复");
        }
        Shop shop = new Shop();
        // 查询企业
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        Integer count = shopService.lambdaQuery().eq(Shop::getEnterpriseId, currentUser.getEnterpriseId())
                .eq(Shop::getMallType, mallType).count();
        if (count > 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "已存在店铺！");
        }
        org.springframework.beans.BeanUtils.copyProperties(dto, shop);
        String shopNameOne = shop.getShopName().substring(0, 1);
        String oneChar = ChineseCharacterUtil.convertHanzi2Pinyin(shopNameOne, false);
        shop.setShopType(currentUser.getEnterpriseType());
        shop.setInitial(oneChar.toUpperCase());
        shop.setIsInternalSettlement(PublicEnum.IS_YES.getCode());
        shop.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_DP.getRemark()));
        if (currentUser.getIsSupplier() != null && currentUser.getIsSupplier() == 1) {
            shop.setIsSupplier(1);
        } else {
            shop.setIsSupplier(0);
        }
        shop.setIsInternalShop(PublicEnum.IS_NO.getCode());
//        shop.setAuditStatus(2);
        shop.setAuditStatus(1);//去除平台初审-平台审核店铺，默认审核通过
        shop.setAuditPassTime(new Date());
        shop.setState(PublicEnum.IS_YES.getCode());
        shop.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        shop.setLinkMan(dto.getRealName());
        shop.setContactNumber(ThreadLocalUtil.getCurrentUser().getUserMobile());
        shop.setIsOtherAuth(JSON.toJSONString(dto.getIsOtherAuth()));
        shop.setOpenDate(new Date());
        int insert = shopMapper.insert(shop);
        if (insert == 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "开店失败！");
        }
        // todo 开店保存缴费记录并记录为未缴费
        if (!CollectionUtils.isEmpty(dto.getFiles())) {
            List<File> files = dto.getFiles();
            for (File file : files) {
                file.setRelevanceId(shop.getShopId());
            }
            boolean b = fileService.saveBatch(files);
            if (!b) {
                throw new BusinessException(400, "附件资料保存失败！");
            }
        }

                // 更新到企业
                if (insert != 0) {
                    EnterpriseInfo enterpriseInfo = enterpriseInfoMapper.selectById(shop.getEnterpriseId());
                    enterpriseInfo.setZcstate("2");
//                    enterpriseInfo.setCardPortraitFace(dto.getIdentityCardFace());
//                    enterpriseInfo.setCardPortraitFaceId(dto.getIdentityCardFaceId());
//                    enterpriseInfo.setCardPortraitNationalEmblem(dto.getIdentityCardBadge());
//                    enterpriseInfo.setCardPortraitNationalEmblemId(dto.getIdentityCardBadgeId());
//                    enterpriseInfo.setAdminName(dto.getRealName());
//                    enterpriseInfo.setAdminNumber(dto.getIdentityCard());
                    enterpriseInfoMapper.updateById(enterpriseInfo);
                }
    }

    /**
     * 根据当前的登陆的用户id查询店铺状态 外部
     *
     * @return
     */
    @Override
    public ShopStateVO getShopStateByUserId() {
        ShopStateVO vo = new ShopStateVO();
        Shop shop = shopService.lambdaQuery()
                .eq(Shop::getEnterpriseId, ThreadLocalUtil.getCurrentUser().getEnterpriseId())
                .eq(Shop::getMallType, mallConfig.mallType)
                .select(Shop::getAuditStatus, Shop::getShopId, Shop::getIsOtherAuth, Shop::getShopClass, Shop::getIsBusiness, Shop::getState)
                .one();
        if (shop != null) {
            vo.setState(shop.getState());
            vo.setShopId(shop.getShopId());
            vo.setAuditStatus(shop.getAuditStatus());
            vo.setShopClass(shop.getShopClass());
            vo.setIsBusiness(shop.getIsBusiness());
            if (!StringUtils.isEmpty(shop.getIsOtherAuth())) {
                Map data = JSONObject.parseObject(shop.getIsOtherAuth(), Map.class);
                vo.setIsOtherAuth(data);
            }
            if (vo.getAuditStatus() == 1) {
                // 如果状态等于1更新redis
                if (ThreadLocalUtil.getCurrentUser().getShopId() == null) {
                    LoginVO vo2 = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallConfig.mallType + "_" + ThreadLocalUtil.getCurrentUser().getFarUserId()), LoginVO.class);
                    if (StringUtils.isEmpty(vo2.getShopId())) {
                        vo2.setShopId(shop.getShopId());
                        vo2.setShopType(shop.getShopType());
                        vo2.setShopName(shop.getShopName());
                        if (!StringUtils.isEmpty(shop.getIsOtherAuth())) {
                            vo2.setIsOtherAuth(JSONObject.parseObject(shop.getIsOtherAuth(), Map.class));
                        }

                        System.out.println("保存了数据：" + JSON.toJSONString(vo2));
                        stringRedisTemplate.opsForValue().set(RedisKey.USER_MAP_KEY + mallConfig.mallType + "_" + vo2.getFarUserId(), JSON.toJSONString(vo2), mallConfig.loginOutTime, TimeUnit.MINUTES);
                    }
                }
            }
        }
        return vo;
    }

    /**
     * 开店回显（内部）
     *
     * @return
     */
    @Override
    public CreateInsideShopEchoInfoVO creatInsideShopEchoInfo() {
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        CreateInsideShopEchoInfoVO vo = new CreateInsideShopEchoInfoVO();
        User user = getById(userId);
        if (user != null) {
            // 查询企业
            LambdaQueryChainWrapper<EnterpriseInfo> q = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId());
            Integer mallType = mallConfig.mallType;
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                q.eq(EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode());
            } else {
                q.eq(EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode());
            }
            EnterpriseInfo enterpriseInfo = q.one();
            if (enterpriseInfo != null) {
                org.springframework.beans.BeanUtils.copyProperties(enterpriseInfo, vo);
            }
        }
        return vo;
    }



    /**
     * 开店（内部
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createShopInside(CreateShopInsideDTO dto) {
        if (!StringUtils.isEmpty(dto.getShopId())) {
            Shop shop = new Shop();
            shop.setShopId(dto.getShopId());
            shop.setAuditStatus(2);
            shop.setGmtModified(new Date());
            shop.setShopName(dto.getShopName());
            String oneChar = ChineseCharacterUtil.convertHanzi2Pinyin(shop.getShopName().substring(0, 1), false);
            shop.setInitial(oneChar.toUpperCase());
            if (mallConfig.mallType == 0) {
                Boolean isBusinessOrg = systemParamService.getIsBusinessOrg(ThreadLocalUtil.getCurrentUser().getOrgId());
                if (isBusinessOrg) {
                    //判断是否是自营店，
                    shop.setIsBusiness(1);
                    //设置店铺为多供方店铺
                    shop.setShopClass(2);
                }
            }
            // 店铺简介
            if (org.apache.commons.lang.StringUtils.isNotBlank(dto.getShopProfile())) {
                shop.setShopDescrible(dto.getShopProfile());
            }
            shop.setLinkMan(dto.getContact());
            shop.setContactNumber(dto.getTel());
            shop.setDetailedAddress(dto.getDetailedAddress());
            shop.setIsInternalShop(1);
            shopService.update(shop);
            return;
        }
        Shop shop = new Shop();
        // 查询企业
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        Integer count = shopService.lambdaQuery().eq(Shop::getEnterpriseId, currentUser.getEnterpriseId())
                .eq(Shop::getMallType, mallConfig.mallType).count();
        if (count > 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "已存在店铺！");
        }
        shop.setShopType(1);
        shop.setShopName(dto.getShopName());
        shop.setLinkMan(dto.getContact());
        shop.setContactNumber(dto.getTel());
        shop.setDetailedAddress(dto.getDetailedAddress());
        String shopNameOne = shop.getShopName().substring(0, 1);
        String oneChar = ChineseCharacterUtil.convertHanzi2Pinyin(shopNameOne, false);
        shop.setInitial(oneChar.toUpperCase());
        shop.setSerialNum(CodeUtil.getCodeByPrefix(CodeEnum.CODE_DP.getRemark()));
        shop.setIsSupplier(1);
        if (mallConfig.mallType == 1) {
            if (mallConfig.isDPlatformAdminOrgId.equals(ThreadLocalUtil.getCurrentUser().getOrgId())) {
                shop.setIsBusiness(1);
            }
        }
        if (mallConfig.mallType == 0) {
            Boolean isBusinessOrg = systemParamService.getIsBusinessOrg(ThreadLocalUtil.getCurrentUser().getOrgId());
            if (isBusinessOrg) {
                shop.setIsBusiness(1);
            }
        }
        shop.setIsInternalShop(PublicEnum.IS_YES.getCode());
        shop.setIsInternalSettlement(PublicEnum.IS_YES.getCode());
        shop.setAuditStatus(2);
        shop.setState(PublicEnum.IS_YES.getCode());
        shop.setEnterpriseId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        shop.setOpenDate(new Date());
        // 店铺简介
        if (org.apache.commons.lang.StringUtils.isNotBlank(dto.getShopProfile())) {
            shop.setShopDescrible(dto.getShopProfile());
        }
        int insert = shopMapper.insert(shop);

        EnterpriseInfo enterprise = enterpriseInfoService.getById(currentUser.getEnterpriseId());
        //
        if (ObjectUtils.isEmpty(dto.getTaxRate())) {
            if (dto.getTaxRate().compareTo(new BigDecimal(0)) <= 0 || dto.getTaxRate().compareTo(new BigDecimal(100)) > 0) {
                throw new BusinessException("税率超出限制");
            }
            enterprise.setTaxRate(dto.getTaxRate());
        }
        enterprise.setIsSupplier(2);
        enterprise.setIsPcwp(1);
        enterpriseInfoService.updateById(enterprise);

        if (insert == 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "开店失败！");
        }
    }


    private void sendCodeUtil(String phone, String code) {
        String sendExchange = mallConfig.sendCodeExchange;
        String message = "{\"Content\":\"登陆验证码：" + code + "，请不要把验证码泄漏给其他人\",\"PhoneNumbers\":[\"" + phone + "\"]}";
        try {
            rabbitTemplate.convertAndSend(sendExchange, "", message);
        } catch (Exception e) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "发送失败");
        }
    }


    /**
     * 手机号登陆
     *
     * @param phone
     * @param code
     * @param mallType
     * @param request
     * @return
     */
    @Override
    public LoginVO phoneLogin(String phone, String code, Integer mallType, HttpServletRequest request) {

        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(code)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请输入验证码！");
        }
        if (verifyCode) {
            String rCode = stringRedisTemplate.opsForValue().get(RedisKey.LOGIN_CODE_KEY + phone);
            if (rCode == null) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码已失效，请重新发送！");
            }
            if (!code.equals(rCode.split("_")[0])) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码错误！");
            }
        }
        LoginVO vo = login(phone, null, mallType, request);
        return vo;
    }

    /**
     * 登陆发送手机验证码
     *
     * @param phone
     * @param privateKeyId
     * @param request
     */
    @Override
    public void loginSendCode(String phone, String privateKeyId, HttpServletRequest request) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (StringUtils.isEmpty(privateKeyId)) {
            throw new BusinessException("非法请求！");
        }
        String rCode = stringRedisTemplate.opsForValue().get("register:privateId" + phone);
        if (rCode == null) {
            throw new BusinessException("获取失败，请重新获取！");
        }
        log.info(this.getClass().getName()+":loginSendCode:"+rCode);
        stringRedisTemplate.delete("register:privateId" + phone);
//        String decrypt = AESUtil.encrypt(rCode);
//        if (!decrypt.equals(decrypt)) {
//            throw new BusinessException("非法请求！");
//        } else {
//            stringRedisTemplate.delete("register:privateId" + phone);
//        }


        // 处理频繁发送
        String redisCode = stringRedisTemplate.opsForValue().get(RedisKey.LOGIN_CODE_KEY + phone);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(redisCode) && System.currentTimeMillis() - Long.parseLong(redisCode.split("_")[1]) < RedisKey.LOGIN_CODE_TTL * 6 * 10000) {
            // 调用接口小于60秒间隔不允许重新发送新的验证码
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请勿频繁发送！");
        }
        // 查询用户是否存在
        Integer count = lambdaQuery().eq(User::getUserMobile, phone).count();
        if (count == 0) {
            log.error("未注册登陆发送短信手机号：" + phone);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "该用户未注册，请注册后再试！");
        } else {
            ifUserSendCode(phone, request);
        }
        // 生成短信验证码
        String code = CommonUtil.getCode();
        // 发送验证码
        sendCodeUtil(phone, code);
        stringRedisTemplate.opsForValue().set(RedisKey.LOGIN_CODE_KEY + phone, code + "_" + System.currentTimeMillis(), RedisKey.LOGIN_CODE_TTL, TimeUnit.MINUTES);
        System.out.println("登陆验证码发送成功！本次验证码为：" + code);
    }

    private void ifUserSendCode(String phone, HttpServletRequest request) {
        User user = lambdaQuery().eq(User::getUserMobile, phone).one();
        // 检验最大次数
        UserActivity u = new UserActivity();
        u.setUserId(user.getUserId());
        u.setUserName(user.getRealName());
        u.setLoginTime(new Date());
        u.setOrgId(user.getEnterpriseId());
        try {
            String ipAddress = request.getHeader("X-Forwarded-For").split(":")[0];
            u.setLoginIp(ipAddress);
        } catch (Exception e) {
        }
        u.setUserMobile(phone);
        u.setBusType(1);
        userActivityService.save(u);
        int maxCodeNum = PublicEnum.MAX_SEND_CODE.getCode();
        Integer count1 = userActivityService.lambdaQuery().eq(UserActivity::getUserMobile, phone)
                .eq(UserActivity::getBusType, 1)
                .apply("DATE_FORMAT(login_time, '%Y-%m-%d')='" + LocalDate.now() + "'").count();
        if (count1 > maxCodeNum) {
            throw new BusinessException("发送短信次数过多！请联系管理员！");
        }
    }

    /**
     * 注册发送手机验证码
     *
     * @param phone
     * @param privateKeyId
     */
    @Override
    public void registerSendCode(String phone, String privateKeyId) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (StringUtils.isEmpty(privateKeyId)) {
            throw new BusinessException("非法请求！");
        }
        String rCode = stringRedisTemplate.opsForValue().get("register:privateId" + phone);
        if (rCode == null) {
            throw new BusinessException("获取失败，请重新获取！");
        }
        log.info(this.getClass().getName()+":registerSendCode:"+rCode);
        stringRedisTemplate.delete("register:privateId" + phone);
//        String decrypt = AESUtil.encrypt(rCode);
//        if (!decrypt.equals(decrypt)) {
//            throw new BusinessException("非法请求！");
//        } else {
//            stringRedisTemplate.delete("register:privateId" + phone);
//        }
        // 处理频繁发送
        String redisCode = stringRedisTemplate.opsForValue().get(RedisKey.REGISTER_CODE_KEY + phone);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(redisCode) && System.currentTimeMillis() - Long.parseLong(redisCode.split("_")[1]) < RedisKey.REGISTER_CODE_TTL * 6 * 10000) {
            // 调用接口小于60秒间隔不允许重新发送新的验证码
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请勿频繁发送！");
        }
        // 注册查询是否注册过
        Integer count = lambdaQuery().eq(User::getUserMobile, phone).count();
        if (count != 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "该用户已注册！");
        }
        // 生成短信验证码
        String code = CommonUtil.getCode();
        // 发送验证码
        sendCodeUtil(phone, code);
        //将验证码存入redis
        stringRedisTemplate.opsForValue().set(RedisKey.REGISTER_CODE_KEY + phone, code + "_" + System.currentTimeMillis(), RedisKey.REGISTER_CODE_TTL, TimeUnit.MINUTES);
        System.out.println("注册验证码发送成功！本次验证码为：" + code);
    }

    /**
     * 根据当前的登陆的用户id获取企业信息
     *
     * @return
     */
    @Override
    public EnterpriseAuthUpdateVO getEnterpriseAuthInfo(Integer mallType) {
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        EnterpriseAuthUpdateVO vo = new EnterpriseAuthUpdateVO();
        User user = getById(userId);
        if (user != null) {
            // 查询企业
            LambdaQueryChainWrapper<EnterpriseInfo> q = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId());
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                q.eq(EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode());
            } else {
                q.eq(EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode());
            }
            EnterpriseInfo enterpriseInfo = q.one();

            if (enterpriseInfo != null) {
                List<File> files = fileService.findBatchFileByEnterPriseInfo(enterpriseInfo);
                if (files != null && files.size() > 0) {
                    enterpriseInfo.setFiles(files);
                } else {
                    List<File> files2 = new ArrayList<>();
                    enterpriseInfo.setFiles(files2);
                }
                List<EnterprisePerformance> list = enterprisePerformanceService.list(new QueryWrapper<EnterprisePerformance>().lambda()
                        .eq(EnterprisePerformance::getEnterpriseId, enterpriseInfo.getEnterpriseId()).orderByDesc(EnterprisePerformance::getGmtCreate));
                if(list != null && list.size() > 0) {
                    for(EnterprisePerformance ep:list){
                        List<Date> ghDatelist = new ArrayList<>();
                        ghDatelist.add(ep.getSupplyStartDate());
                        ghDatelist.add(ep.getSupplyEndDate());
                        ep.setGhdate(ghDatelist);
                    }
                }
                enterpriseInfo.setEpLists(list);
                enterpriseInfo.setCertificate(Arrays.asList(enterpriseInfo.getCertificateType().split(",")));
                org.springframework.beans.BeanUtils.copyProperties(enterpriseInfo, vo);
            }
        }
        return vo;
    }

    @Override
    public User getUserData() {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        User userInfo = userService.getById(user.getUserId());
        return userInfo;
    }


    @Override
    public void test() {
        test2();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void test2() {
        User user = new User();
        user.setUserNumber("text1111");
        save(user);
        if (1 == 1) {
            throw new BusinessException(400, "错误");
        }
        User user2 = new User();
        user2.setUserNumber("text222222");
        save(user2);
    }

    @Override
    public void updateUserImg(String userId, String userImg) {
        User user = getById(userId);
        user.setUserImg(userImg);
        update(user);
    }

    /**
     * 获取用户的信息企业信息店铺信息
     *
     * @return
     */
    @Override
    public UserShopEnterpriseInfoVO getUserShopEnterpriseInfo() {
        UserShopEnterpriseInfoVO vo = new UserShopEnterpriseInfoVO();
        User user = getById(ThreadLocalUtil.getCurrentUser().getUserId());
        if (user != null) {
            vo.setUser(user);
            LambdaQueryChainWrapper<EnterpriseInfo> q = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId());
            Integer mallType = mallConfig.mallType;
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                q.eq(EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode());
            } else {
                q.eq(EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode());
            }
            EnterpriseInfo enterpriseInfo = q.one();
            if (enterpriseInfo != null) {
                vo.setEnterpriseInfo(enterpriseInfo);
                Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId()).eq(Shop::getMallType, mallType).one();
                vo.setShop(shop);
            }
        }
        return vo;
    }

    /**
     * 用户退出登陆
     */
    @Override
    public void loginOut() {
        stringRedisTemplate.delete(RedisKey.USER_MAP_KEY + mallConfig.mallType + "_" + ThreadLocalUtil.getCurrentUser().getFarUserId());
    }

    /**
     * tt登陆
     *
     * @param a
     * @param p
     * @param mallType
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginVO ttLogin(String a, String p, Integer mallType, HttpServletRequest request) {

        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        Integer userCount = userService.lambdaQuery()
                .and((t) -> t.eq(User::getAccount, a)
                        .or()
                        .eq(User::getUserMobile, a))
                .eq(mallType == 0, User::getMaterialState, 0)
                .eq(mallType == 1, User::getDeviceState, 0).count();
        if (userCount > 0) {
            throw new BusinessException(400, "该用户已被停用，禁止登陆！");
        }
        String client_id = null;
        if (mallType == 1) {
            client_id = "emcp";
        }
        if (mallType == 0) {
            client_id = "mmcp";
        }
        String client_secret = "Ms1q2w3e";
        String grant_type = "password";
        String scope = "openid profile.ext email profile sso.sts";
        String username = a;
        String password = p;
//        String url = "https://login.scrbg.com/connect/token?" +
//                "client_id=" + client_id + "&" +
//                "client_secret=" + client_secret + "&" +
//                "grant_type=" + grant_type + "&" +
//                "scope=" + scope + "&" +
//                "username=" + username + "&" +
//                "password=" + password ;
        String url = "https://login.scrbg.com/connect/token";
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("client_id", client_id);
        postParameters.add("client_secret", client_secret);
        postParameters.add("grant_type", grant_type);
        postParameters.add("scope", scope);
        postParameters.add("username", username);
        postParameters.add("password", password);
        HttpHeaders headers0 = new HttpHeaders();
        headers0.add("Content-Type", "application/x-www-form-urlencoded");
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(postParameters, headers0);
        Map rMap = restTemplate.postForObject(url, entity, Map.class);


        // 解析token
        String url2 = "https://login.scrbg.com/connect/userinfo";
        HttpHeaders headersxx = new HttpHeaders();
        String accessToken = (String) rMap.get("access_token");
        headersxx.set("Authorization", "Bearer " + accessToken);
        HttpEntity<String> requestEntity = new HttpEntity<>(null, headersxx);
        Map userInfoMapBody = restTemplate.exchange(url2, HttpMethod.GET, requestEntity, Map.class).getBody();


        // 员工号
        String employee_number = (String) userInfoMapBody.get("employee_number");
        // 身份证号
        String idcard_number = (String) userInfoMapBody.get("idcard_number");
        // 换取token
        String url3 = mallConfig.prodPcwp2Url + "/identity/auth/createToken?employeeNo="
                + employee_number + "&idNo=" + idcard_number;
        HttpHeaders thisHeaders = new HttpHeaders();
        thisHeaders.add("sysCode", "msp");
        HttpEntity<String> thisRequest = new HttpEntity<>(thisHeaders);
        R<Map> r = restTemplate.postForObject(url3, thisRequest, R.class);
        if (r.getCode() == 200) {
            LoginVO vo = BeanUtils.mapToBean(r.getData(), LoginVO.class);
            vo.setOriginalUserName(vo.getUserName());
            String account = vo.getUserNumber();
            vo.setFarUserId(vo.getUserId());
            vo.setUserNumber(account);
            Integer isInterior = 1;
            vo.setIsInterior(isInterior);
            vo.setIsExternal(0);
            // 是内部用户
            if (isInterior == 1) {
                if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId())) {
                    LoginVO loginVO = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId()), LoginVO.class);
                    log.warn("已登陆，直接登陆成功！");
                    return loginVO;
                }
                // 内部都是企业
                vo.setEnterpriseType(1);
//                // 内部都不是供应商
//                vo.setIsSupplier(0);
                User qUser = userService.lambdaQuery().eq(User::getInteriorId, vo.getUserId()).one();
                String orgUrl = mallConfig.prodPcwp2Url + "/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
//                String orgUrl = "http://pcwp2-api.scrbg.com/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
                R orgR = restTemplate.getForObject(orgUrl, R.class);
                if (orgR.getCode() == 200) {
                    // 第一个就是当前企业
                    ArrayList orgArr = (ArrayList) orgR.getData();
                    if (!CollectionUtils.isEmpty(orgArr)) {
                        Map orgMap = (Map) orgArr.get(0);
                        vo.setOrgInfo(orgMap);
                        // 设置当前机构
                        String orgId = (String) orgMap.get("orgId");
                        String orgName = (String) orgMap.get("orgName");
                        String orgNumber = (String) orgMap.get("shortCode");
                        vo.setOrgId(orgId);
                        vo.setOrgName(orgName);
                        vo.setOrgNumber(orgNumber);
                        HttpHeaders headers = new HttpHeaders();
                        headers.add("token", vo.getToken());
                        headers.add("org", JSONObject.toJSONString(orgMap));
                        if (mallType == 1) {
                            headers.add("sysCode", "egp");
                        }
                        if (mallType == 0) {
                            headers.add("sysCode", "msp");
                        }
                        // 配置角色数据
                        configRoteInfo(mallType, vo, headers);

                        // 本机构以及其下机构列表
                        String orgCUrl = mallConfig.prodPcwp2Url + "/hr/org/getChildrenOrg?orgId=" + orgId
                                + "&orgName=" + orgName;
                        ResponseEntity<R> mapResponseEntity = restTemplateUtils.get(orgCUrl, headers, R.class);
                        R thisAndChild = mapResponseEntity.getBody();
                        if (thisAndChild.getCode() == 200) {
                            ArrayList childList = (ArrayList) thisAndChild.getData();
                            ArrayList<String> orgIds = new ArrayList<>();
                            ArrayList<OrgAndSon> orgAndSons = new ArrayList<>();
                            if (!CollectionUtils.isEmpty(childList)) {
                                for (Object o : childList) {
                                    Map ch = (Map) o;
                                    orgIds.add((String) ch.get("orgId"));
                                    OrgAndSon orgAndSon = new OrgAndSon();
                                    orgAndSon.setOrgId((String) ch.get("orgId"));
                                    orgAndSon.setOrgName((String) ch.get("orgName"));
                                    orgAndSon.setShortCode((String) ch.get("shortCode"));
                                    orgAndSons.add(orgAndSon);
                                }
                            }
                            // 当前机构id以及子机构id
                            vo.setOrgIds(orgIds);
                            vo.setOrgAndSon(orgAndSons);
                        }
                        // 设置机构列表
                        ArrayList<OrganizationVO> organizationVOS = new ArrayList<>();
                        boolean orgFlag = false;
                        for (Object o : orgArr) {
                            Map map = (Map) o;
                            OrganizationVO organizationVO = new OrganizationVO();
                            organizationVO.setOrgId((String) map.get("orgId"));
                            organizationVO.setOrgName((String) map.get("orgName"));
                            organizationVO.setShortCode((String) map.get("shortCode"));
                            organizationVO.setOrgType((Integer) map.get("orgType"));
                            // 物资
                            if (mallType == 0) {
                                if (mallConfig.isBusinessOrg==1){
                                    Boolean flag = systemParamService.getIsBusinessOrg(organizationVO.getOrgId());
                                    if (flag) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }else {
                                    if (mallConfig.isMPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }
                            }
                            // 装备
                            if (mallType == 1) {
                                if (mallConfig.isDPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                    orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                }
                            }
//                            }
                            organizationVOS.add(organizationVO);
                        }
                        if (orgFlag) {
                            vo.setIsPlatformAdmin(1);
                        } else {
                            vo.setIsPlatformAdmin(0);
                        }
                        // 特殊处理账号权限
                        List<String> authArr = new ArrayList<>();
                        AuthArrUtil.addAuth(authArr);
//                        authArr.add("wisesoft");
//                        authArr.add("005718");
//                        authArr.add("srbg5718");
//                        authArr.add("032232");
//                        authArr.add("laizq0119");
                        if (authArr.contains(account)){
                            vo.setIsPlatformAdmin(1);
                        }
                        vo.setOrganizationVOS(organizationVOS);
                    }
                } else {
                    throw new BusinessException(orgR.getCode(), orgR.getMessage());
                }
                if (qUser == null) {
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    if (enterpriseInfo == null) {
                        // 企业不存在
                        EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                        // 企业编号
                        enterpriseInfo1.setCreationTime(new Date());
                        enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                        enterpriseInfo1.setInteriorId(vo.getOrgId());
                        enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                        enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                        enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                        } else {
                            enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                        }
                        enterpriseInfo1.setEnterpriseType(1);
                        enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                        int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                        if (insert != 0) {
                            vo.setLocalOrgId(enterpriseInfo1.getEnterpriseId());
                            vo.setEnterpriseName(enterpriseInfo1.getEnterpriseName());
                            vo.setEnterpriseType(enterpriseInfo1.getEnterpriseType());
                            // 如果用户不存在则保存内部用户数据
                            User user = new User();
                            user.setAccount(account);
                            user.setPassword(password);
                            user.setInteriorId(vo.getUserId());
                            user.setNickName(vo.getUserName());
                            user.setEnterpriseId(enterpriseInfo1.getEnterpriseId());
                            user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                            // 状态
                            user.setIsInternalUser(isInterior);
                            user.setState(PublicEnum.IS_YES.getCode());
                            if (mallType == PublicEnum.MATERIALS.getCode()) {
                                user.setIsMaterial(PublicEnum.IS_YES.getCode());
                            } else {
                                user.setIsDevice(PublicEnum.IS_YES.getCode());
                            }
                            boolean save = userService.save(user);
                            if (save) {
                                vo.setUserId(user.getUserId());
                                vo.setUserName(user.getNickName());
                                vo.setUserNumber(user.getUserNumber());
                            }
                        }
                    } else {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                            log.error("不是供应商1232:" + enterpriseInfo);
                            log.error("不是供应商1232:" + a);
                            log.error("不是供应商1232:" + p);
                            log.error("不是供应商1232:" + vo);
                        }
                        // 企业存在用户不存在
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 如果用户不存在则保存内部用户数据
                        User user = new User();
                        user.setAccount(account);
                        user.setPassword(password);
                        user.setInteriorId(vo.getUserId());
                        user.setNickName(vo.getUserName());
                        user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                        user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                        user.setIsInternalUser(isInterior);
                        user.setState(PublicEnum.IS_YES.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            user.setIsMaterial(PublicEnum.IS_YES.getCode());
                        } else {
                            user.setIsDevice(PublicEnum.IS_YES.getCode());
                        }
                        boolean save = userService.save(user);
                        if (save) {
                            vo.setUserId(user.getUserId());
                            vo.setUserName(user.getNickName());
                            vo.setUserNumber(user.getUserNumber());
                        }
                    }
                } else {
                    // 用户存在
                    userIsEnable(vo.getUserNumber());
                    vo.setUserId(qUser.getUserId());
                    vo.setUserName(qUser.getNickName());
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    // TODO 用户存在但是企业不存在
                    if(enterpriseInfo == null){
                        // 企业不存在
                        // 企业不存在
                        EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                        // 企业编号
                        enterpriseInfo1.setCreationTime(new Date());
                        enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                        enterpriseInfo1.setInteriorId(vo.getOrgId());
                        enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                        enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                        enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                        } else {
                            enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                        }
                        enterpriseInfo1.setEnterpriseType(1);
                        enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                        int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                        if (insert!=0){
                            enterpriseInfo = enterpriseInfo1;
                        }
                    }
                    if (enterpriseInfo != null) {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                            log.error("不是供应商1232:" + enterpriseInfo);
                            log.error("不是供应商1232:" + a);
                            log.error("不是供应商1232:" + p);
                            log.error("不是供应商1232:" + vo);
                        }
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 每次登陆修改回当前机构
                        LambdaUpdateChainWrapper<User> setL = userService.lambdaUpdate()
                                .eq(User::getUserId, qUser.getUserId())
                                .set(User::getEnterpriseId, enterpriseInfo.getEnterpriseId());
                        // 从token 登陆进来可能不会保存用户账号，如果这里用户但是账号或手机号不存在则需要保存
                        String account1 = qUser.getAccount();
                        String userMobile = qUser.getUserMobile();
                        if (StringUtils.isEmpty(account1) && StringUtils.isEmpty(userMobile)) {
                            // 如果账号手机号都不存在则保存
                            setL.set(User::getAccount, account);
                        }
                        setL.update();
                        if (vo.getIsShpAuthority() == 1) {
                            Shop shopDate = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .one();
                            if (shopDate != null) {
                                vo.setShopId(shopDate.getShopId());
                                vo.setShopType(shopDate.getShopType());
                                vo.setShopName(shopDate.getShopName());
                            }
                        }
                    }
                }
            } else {
                // TODO TT登陆没有外部用户
            }
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            boolean material = false;
            boolean device = false;
            // 修改
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                material = true;
            } else {
                device = true;
            }
            userService.lambdaUpdate()
                    .eq(User::getUserId, vo.getUserId())
                    .set(material, User::getIsMaterial, PublicEnum.IS_YES.getCode())
                    .set(device, User::getIsDevice, PublicEnum.IS_YES.getCode())
                    .setSql("login_count = login_count + 1")
                    .set(User::getGmtLogin, new Date()).update();

            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            if (vo.getOrgInfo() != null) {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(EnterpriseInfo::getShortCode, vo.getOrgInfo().get("shortCode"))
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            } else {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            }
            userActivityM(vo, "tt登陆", request);
            return vo;
        } else {
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }

    /**
     * 切换企业
     *
     * @param dto
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginVO cutOrg(OrganizationDTO dto, HttpServletRequest request) {
        String orgId = dto.getOrgId();
        String orgName = dto.getOrgName();
        String orgNumber = dto.getShortCode();
        // 要切换的当前orgJson
        log.error("当前机构：" + dto);
        String orgJson = JSONObject.toJSONString(dto);
        log.error("当前机构JSON：" + orgJson);
        LoginVO vo = new LoginVO();
        Integer mallType = dto.getMallType();
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();

        // 旧数据
        LoginVO formerVO = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + currentUser.getFarUserId()), LoginVO.class);
        if (formerVO == null) {
            throw new BusinessException("登录过期，请重新登陆！");
        }
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (dto.getUserId() != null) {
            if (!dto.getUserId().equals(user.getUserId())) {
                throw new BusinessException("当前登陆用户发生变化，请重新登陆！");
            }
        }
        Integer isInterior = formerVO.getIsInterior();
        if (isInterior == null || isInterior != 1) {
            throw new BusinessException("不是内部用户，不能切换机构，请重新登陆！");
        }
        boolean orgFlag1 = false;
        List<OrganizationVO> organizationVOS1 = formerVO.getOrganizationVOS();
        if (CollectionUtils.isEmpty(organizationVOS1)) {
            throw new BusinessException("未检测到机构信息，请重新登陆！");
        }
        for (OrganizationVO organizationVO : organizationVOS1) {
            if (dto.getOrgId().equals(organizationVO.getOrgId())) {
                orgFlag1 = true;
            }
        }
        if (orgFlag1 == false) {
            throw new BusinessException("当前用户不存在该机构，请退出登陆再试！");
        }
        if (user.getIsInterior() == null || user.getIsInterior() != 1) {
            throw new BusinessException("不是内部用户，不能切换机构！");
        }
        if (formerVO == null) {
            throw new BusinessException(400, "登录过期，请重新登陆！");
        }
        org.springframework.beans.BeanUtils.copyProperties(formerVO, vo);
        // 设置新的机构信息
        BeanMap beanMap = BeanMap.create(dto);
        HashMap<Object, Object> orgInfo = new HashMap<>(beanMap);
        vo.setOrgInfo(orgInfo);
        vo.setOrgId(orgId);
        vo.setOrgName(orgName);
        vo.setOrgNumber(orgNumber);
        HttpHeaders headers = new HttpHeaders();
        headers.add("token", vo.getToken());
        headers.add("org", orgJson);
        if (mallType == 1) {
            headers.add("sysCode", "egp");
        }
        if (mallType == 0) {
            headers.add("sysCode", "msp");
        }
        //当前组织以及子组织ids
        String orgCUrl = mallConfig.prodPcwp2Url + "/hr/org/getChildrenOrg?orgId=" + orgId
                + "&orgName=" + orgName;
        ResponseEntity<R> mapResponseEntity = restTemplateUtils.get(orgCUrl, headers, R.class);
        R thisAndChild = mapResponseEntity.getBody();
        if (thisAndChild.getCode() == 200) {
            ArrayList childList = (ArrayList) thisAndChild.getData();
            ArrayList<String> orgIds = new ArrayList<>();
            ArrayList<OrgAndSon> orgAndSons = new ArrayList<>();
            if (!CollectionUtils.isEmpty(childList)) {
                for (Object o : childList) {
                    Map ch = (Map) o;
                    orgIds.add((String) ch.get("orgId"));
                    OrgAndSon orgAndSon = new OrgAndSon();
                    orgAndSon.setOrgId((String) ch.get("orgId"));
                    orgAndSon.setOrgName((String) ch.get("orgName"));
                    orgAndSon.setShortCode((String) ch.get("shortCode"));
                    orgAndSons.add(orgAndSon);
                }
            }
            // 当前机构id以及子机构id
            vo.setOrgIds(orgIds);
            vo.setOrgAndSon(orgAndSons);
        }
        // 配置角色数据
        configRoteInfo(mallType, vo, headers);

        List<OrganizationVO> organizationVOS = vo.getOrganizationVOS();

        // 切换企业不能是当前企业
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                .eq(EnterpriseInfo::getInteriorId, orgId).one();
        if (enterpriseInfo == null) {
            vo.setIsSupplier(0);
            // 企业不存在
            EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
            // 企业编号
            enterpriseInfo1.setCreationTime(new Date());
            enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
            enterpriseInfo1.setInteriorId(orgId);
            enterpriseInfo1.setEnterpriseName(orgName);
            enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
            enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
            } else {
                enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
            }
            enterpriseInfo1.setEnterpriseType(1);
            enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
            int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
            if (insert > 0) {
                boolean update = userService.lambdaUpdate().eq(User::getUserId, currentUser.getUserId())
                        .set(User::getEnterpriseId, enterpriseInfo1.getEnterpriseId())
                        .set(User::getGmtModified, new Date()).update();
                if (update) {
                    vo.setLocalOrgId(enterpriseInfo1.getEnterpriseId());
                    vo.setEnterpriseName(enterpriseInfo1.getEnterpriseName());
                    vo.setEnterpriseType(enterpriseInfo1.getEnterpriseType());
                    if (vo.getIsShpAuthority() != null && vo.getIsShpAuthority() == 1) {
                        Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                .eq(Shop::getMallType, mallType)
                                .one();
                        if (shop != null) {
                            vo.setShopId(shop.getShopId());
                            vo.setShopType(shop.getShopType());
                            vo.setShopName(shop.getShopName());
                        }
                    }
                } else {
                    throw new BusinessException(400, "切换企业失败！");
                }
            } else {
                throw new BusinessException(400, "切换企业失败！");
            }
        } else {
            if (!enterpriseInfo.getEnterpriseName().equals(dto.getOrgName())){
                enterpriseInfo.setEnterpriseName(dto.getOrgName());
                enterpriseInfoService.updateById(enterpriseInfo);
            }
            if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                vo.setIsSupplier(1);
            } else {
                vo.setIsSupplier(0);
            }
            vo.setIsPcwp(enterpriseInfo.getIsPcwp());
            vo.setEnterpriseId(enterpriseInfo.getEnterpriseId());
            vo.setZcstate(enterpriseInfo.getZcstate());
            // 企业存在
            boolean update = userService.lambdaUpdate().eq(User::getUserId, currentUser.getUserId())
                    .set(User::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                    .set(User::getGmtModified, new Date()).update();
            if (update) {
                vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                if (vo.getIsShpAuthority() != null && vo.getIsShpAuthority() == 1) {
                    if (vo.getIsShpAuthority() == 1) {
                        Shop shopDate = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                .one();
                        if (shopDate != null) {
                            vo.setShopId(shopDate.getShopId());
                            vo.setShopType(shopDate.getShopType());
                            vo.setShopName(shopDate.getShopName());
                        }
                    }
                }
            } else {
                throw new BusinessException(400, "切换企业失败！");
            }
        }
        stringRedisTemplate.opsForValue()
                .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
        boolean material = false;
        boolean device = false;
        // 修改
        if (mallType == PublicEnum.MATERIALS.getCode()) {
            material = true;
        } else {
            device = true;
        }
        userService.lambdaUpdate()
                .eq(User::getUserId, vo.getUserId())
                .set(material, User::getIsMaterial, PublicEnum.IS_YES.getCode())
                .set(device, User::getIsDevice, PublicEnum.IS_YES.getCode())
                .set(User::getGmtLogin, new Date()).update();

        stringRedisTemplate.opsForValue()
                .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
        if (vo.getOrgInfo() != null) {
            enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                    .set(EnterpriseInfo::getShortCode, vo.getOrgInfo().get("shortCode"))
                    .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                    .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
        } else {
            enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                    .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                    .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
        }
        userActivityM(vo, "切换企业", request);
        return vo;
    }

    /**
     * 记录登录日志信息
     *
     * @param vo
     * @param loginTypeStr
     */
    public void userActivityM(LoginVO vo, String loginTypeStr, HttpServletRequest request) {
        UserActivity u = new UserActivity();
        u.setUserId(vo.getUserId());
        u.setUserName(vo.getUserName());
        u.setLoginTime(new Date());
        u.setOrgId(vo.getLocalOrgId());
        u.setOrgName(vo.getOrgName());
        u.setOrgType(vo.getEnterpriseType());
        u.setLoginType(loginTypeStr);
        try {
            String ipAddress = request.getHeader("X-Forwarded-For").split(":")[0];
            u.setLoginIp(ipAddress);
        } catch (Exception e) {
        }
        u.setUserMobile(vo.getUserMobile());
        userActivityService.save(u);
    }

    /**
     * token登陆
     *
     * @param token
     * @param mallType
     * @param request
     * @return
     */
    @Override
    public LoginVO tokenLogin(String token, Integer mallType, HttpServletRequest request) {
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        String url = mallConfig.prodPcwp2Url + "/identity/auth/verifyToken?token=" + token;
        R<Map> r = restTemplate.postForObject(url, null, R.class);
        if (r.getCode() == 200) {
            LoginVO vo = BeanUtils.mapToBean(r.getData(), LoginVO.class);
            vo.setOriginalUserName(vo.getUserName());
            Integer userCount = userService.lambdaQuery()
                    .eq(User::getInteriorId, vo.getUserId())
                    .eq(mallType == 0, User::getMaterialState, 0)
                    .eq(mallType == 1, User::getDeviceState, 0).count();
            if (userCount > 0) {
                throw new BusinessException(400, "该用户已被停用，禁止登陆！");
            }
            vo.setFarUserId(vo.getUserId());
            vo.setUserNumber(vo.getUserNumber());
            vo.setIsInterior(1);
            vo.setIsExternal(0);
            // 内部都是企业
            vo.setEnterpriseType(1);
            // 内部都不是供应商
            vo.setIsSupplier(0);
            User qUser = userService.lambdaQuery().eq(User::getInteriorId, vo.getUserId()).one();
            String orgUrl = mallConfig.prodPcwp2Url + "/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
//            String orgUrl = "http://pcwp2-api.scrbg.com/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
            R orgR = restTemplate.getForObject(orgUrl, R.class);
            if (orgR.getCode() == 200) {
                ArrayList orgArr = (ArrayList) orgR.getData();
                // 第一个就是当前企业
                if (!CollectionUtils.isEmpty(orgArr)) {
                    Map orgMap = (Map) orgArr.get(0);
                    vo.setOrgInfo(orgMap);
                    // 设置当前机构
                    String orgId = (String) orgMap.get("orgId");
                    String orgName = (String) orgMap.get("orgName");
                    String orgNumber = (String) orgMap.get("shortCode");
                    vo.setOrgId(orgId);
                    vo.setOrgName(orgName);
                    vo.setOrgNumber(orgNumber);
                    HttpHeaders headers = new HttpHeaders();
                    headers.add("token", vo.getToken());
                    headers.add("org", JSONObject.toJSONString(orgMap));
                    if (mallType == 1) {
                        headers.add("sysCode", "egp");
                    }
                    if (mallType == 0) {
                        headers.add("sysCode", "msp");
                    }

                    // 配置角色数据
                    configRoteInfo(mallType, vo, headers);
                    // 本机构以及其下机构列表
                    String orgCUrl = mallConfig.prodPcwp2Url + "/hr/org/getChildrenOrg?orgId=" + orgId
                            + "&orgName=" + orgName;
                    ResponseEntity<R> mapResponseEntity = restTemplateUtils.get(orgCUrl, headers, R.class);
                    R thisAndChild = mapResponseEntity.getBody();
                    if (thisAndChild.getCode() == 200) {
                        ArrayList childList = (ArrayList) thisAndChild.getData();
                        ArrayList<String> orgIds = new ArrayList<>();
                        ArrayList<OrgAndSon> orgAndSons = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(childList)) {
                            for (Object o : childList) {
                                Map ch = (Map) o;
                                orgIds.add((String) ch.get("orgId"));
                                OrgAndSon orgAndSon = new OrgAndSon();
                                orgAndSon.setOrgId((String) ch.get("orgId"));
                                orgAndSon.setOrgName((String) ch.get("orgName"));
                                orgAndSon.setShortCode((String) ch.get("shortCode"));
                                orgAndSons.add(orgAndSon);
                            }
                        }
                        // 当前机构id以及子机构id
                        vo.setOrgIds(orgIds);
                        vo.setOrgAndSon(orgAndSons);
                    }
                    // 设置机构列表
                    ArrayList<OrganizationVO> organizationVOS = new ArrayList<>();
                    boolean orgFlag = false;
                    for (Object o : orgArr) {
                        Map map = (Map) o;
                        OrganizationVO organizationVO = new OrganizationVO();
                        organizationVO.setOrgId((String) map.get("orgId"));
                        organizationVO.setOrgName((String) map.get("orgName"));
                        organizationVO.setShortCode((String) map.get("shortCode"));
                        organizationVO.setOrgType((Integer) map.get("orgType"));
//                        Integer isShpAuthority = vo.getIsShpAuthority();
//                        if (isShpAuthority != null && isShpAuthority == 1) {
                        // 物资
                        if (mallType == 0) {
                            if (mallConfig.isBusinessOrg==1){
                                Boolean flag = systemParamService.getIsBusinessOrg(organizationVO.getOrgId());
                                if (flag) {
                                    orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                }
                            }else {
                                if (mallConfig.isMPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                    orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                }
                            }
                        }
                        // 装备
                        if (mallType == 1) {
                            if (mallConfig.isDPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                orgFlag = isOrgFlag(mallType, vo, organizationVO);
                            }
                        }
//                        }
                        organizationVOS.add(organizationVO);
                    }
                    if (orgFlag) {
                        vo.setIsPlatformAdmin(1);
                    } else {
                        vo.setIsPlatformAdmin(0);
                    }
                    // 特殊处理账号权限
                    List<String> authArr = new ArrayList<>();
                    AuthArrUtil.addAuth(authArr);
//                    authArr.add("wisesoft");
//                    authArr.add("005718");
//                    authArr.add("srbg5718");
//                    authArr.add("032232");
//                    authArr.add("laizq0119");
                    if (authArr.contains(vo.getOriginalUserName())){
                        vo.setIsPlatformAdmin(1);
                    }
                    vo.setOrganizationVOS(organizationVOS);
                }
            } else {
                throw new BusinessException(orgR.getCode(), orgR.getMessage());
            }
            if (qUser == null) {
                EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                        .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                if (enterpriseInfo == null) {
                    // 企业不存在
                    EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                    // 企业编号
                    enterpriseInfo1.setCreationTime(new Date());
                    enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                    enterpriseInfo1.setInteriorId(vo.getOrgId());
                    enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                    enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                    enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                    if (mallType == PublicEnum.MATERIALS.getCode()) {
                        enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                    } else {
                        enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                    }
                    enterpriseInfo1.setEnterpriseType(1);
                    enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                    int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                    if (insert != 0) {
                        vo.setLocalOrgId(enterpriseInfo1.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo1.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo1.getEnterpriseType());
                        // 如果用户不存在则保存内部用户数据
                        User user = new User();
                        user.setInteriorId(vo.getUserId());
                        user.setNickName(vo.getUserName());
                        user.setEnterpriseId(enterpriseInfo1.getEnterpriseId());
                        user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                        // 状态
                        user.setIsInternalUser(1);
                        user.setState(PublicEnum.IS_YES.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            user.setIsMaterial(PublicEnum.IS_YES.getCode());
                        } else {
                            user.setIsDevice(PublicEnum.IS_YES.getCode());
                        }
                        boolean save = userService.save(user);
                        if (save) {
                            vo.setUserId(user.getUserId());
                            vo.setUserName(user.getNickName());
                        }
                    }
                } else {
                    // 企业存在用户不存在
                    vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                    vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                    vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                    // 如果用户不存在则保存内部用户数据
                    User user = new User();
                    user.setInteriorId(vo.getUserId());
                    user.setNickName(vo.getUserName());
                    user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                    user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                    user.setIsInternalUser(1);
                    user.setState(PublicEnum.IS_YES.getCode());
                    if (mallType == PublicEnum.MATERIALS.getCode()) {
                        user.setIsMaterial(PublicEnum.IS_YES.getCode());
                    } else {
                        user.setIsDevice(PublicEnum.IS_YES.getCode());
                    }
                    boolean save = userService.save(user);
                    if (save) {
                        vo.setUserId(user.getUserId());
                        vo.setUserName(user.getNickName());
                    }
                }
            } else {
                // 用户存在
                userIsEnable(vo.getUserNumber());
                vo.setUserId(qUser.getUserId());
                vo.setUserName(qUser.getNickName());
                EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                        .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                // TODO 用户存在但是企业不存在
                if(enterpriseInfo == null){
                    // 企业不存在
                    // 企业不存在
                    EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                    // 企业编号
                    enterpriseInfo1.setCreationTime(new Date());
                    enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                    enterpriseInfo1.setInteriorId(vo.getOrgId());
                    enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                    enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                    enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                    if (mallType == PublicEnum.MATERIALS.getCode()) {
                        enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                    } else {
                        enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                    }
                    enterpriseInfo1.setEnterpriseType(1);
                    enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                    int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                    if (insert!=0){
                        enterpriseInfo = enterpriseInfo1;
                    }
                }
                if (enterpriseInfo != null) {
                    vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                    vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                    vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                    userService.lambdaUpdate().eq(User::getUserId, qUser.getUserId())
                            .set(User::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                            .update();
                    if (vo.getIsShpAuthority() == 1) {
                        if (mallType == 0) {
                            Shop shopDate = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .one();
                            if (shopDate != null) {
                                vo.setShopId(shopDate.getShopId());
                                vo.setShopType(shopDate.getShopType());
                                vo.setShopName(shopDate.getShopName());
                            }
                        }
                        if (mallType == 1) {
                            Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .eq(Shop::getMallType, mallType)
                                    .one();
                            if (shop != null) {
                                vo.setShopId(shop.getShopId());
                                vo.setShopType(shop.getShopType());
                                vo.setShopName(shop.getShopName());
                            }
                        }
                    }
                }
            }
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);

            boolean material = false;
            boolean device = false;
            // 修改
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                material = true;
            } else {
                device = true;
            }
            userService.lambdaUpdate()
                    .eq(User::getUserId, vo.getUserId())
                    .set(material, User::getIsMaterial, PublicEnum.IS_YES.getCode())
                    .set(device, User::getIsDevice, PublicEnum.IS_YES.getCode())
                    .setSql("login_count = login_count + 1")
                    .set(User::getGmtLogin, new Date()).update();

            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            if (vo.getOrgInfo() != null) {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(EnterpriseInfo::getShortCode, vo.getOrgInfo().get("shortCode"))
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            } else {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            }
            userActivityM(vo, "token登陆", request);
            return vo;
        } else {
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }

    /**
     * 获取当前登陆用户名称和头像
     *
     * @return
     */
    @Override
    public UserNameAndImgVO getUserNameInfo() {
        User user = lambdaQuery().eq(User::getUserId, ThreadLocalUtil.getCurrentUser().getUserId())
                .select(User::getNickName, User::getUserImg).one();
        UserNameAndImgVO vo = new UserNameAndImgVO();
        if (user != null) {
            vo.setUserImg(user.getUserImg());
            vo.setNickName(user.getNickName());
            return vo;
        }
        return vo;
    }

    /**
     * 修改密码发送验证码
     *
     * @param phone
     * @param privateKeyId
     * @param request
     */
    @Override
    public void updatePassSendCode(String phone, String privateKeyId, HttpServletRequest request) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (!phone.equals(ThreadLocalUtil.getCurrentUser().getUserMobile())) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号与当前用户不一致！");
        }
        if (StringUtils.isEmpty(privateKeyId)) {
            throw new BusinessException("非法请求！");
        }
        String rCode = stringRedisTemplate.opsForValue().get("register:privateId" + phone);
        if (rCode == null) {
            throw new BusinessException("获取失败，请重新获取！");
        }
        log.info(this.getClass().getName()+":sendWxLoginCode:"+rCode);
        stringRedisTemplate.delete("register:privateId" + phone);
//        String decrypt = AESUtil.encrypt(rCode);
//        if (!decrypt.equals(decrypt)) {
//            throw new BusinessException("非法请求！");
//        } else {
//            stringRedisTemplate.delete("register:privateId" + phone);
//        }


        // 处理频繁发送
        String redisCode = stringRedisTemplate.opsForValue().get(RedisKey.UPDATE_PASSWORD_CODE_KEY + phone);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(redisCode) && System.currentTimeMillis() - Long.parseLong(redisCode.split("_")[1]) < RedisKey.UPDATE_PASSWORD_CODE_TTL * 6 * 10000) {
            // 调用接口小于60秒间隔不允许重新发送新的验证码
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请勿频繁发送！");
        }
        ifUserSendCode(phone, request);
        // 生成短信验证码
        String code = CommonUtil.getCode();
        // 发送验证码
        sendCodeUtil(phone, code);
        //将验证码存入redis
        stringRedisTemplate.opsForValue().set(RedisKey.UPDATE_PASSWORD_CODE_KEY + phone, code + "_" + System.currentTimeMillis(), RedisKey.UPDATE_PASSWORD_CODE_TTL, TimeUnit.MINUTES);
        log.info(this.getClass().getName()+":updatePassSendCode 修改密码验证码发送成功！本次验证码为：" + code);
    }



    @Override
    public void noUpdatePassSendCode(String phone, String privateKeyId, HttpServletRequest request) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (StringUtils.isEmpty(privateKeyId)) {
            throw new BusinessException("非法请求！");
        }
        String rCode = stringRedisTemplate.opsForValue().get("register:privateId" + phone);
        if (rCode == null) {
            throw new BusinessException("获取失败，请重新获取！");
        }
        log.info(this.getClass().getName()+":noUpdatePassSendCode:"+rCode);
        stringRedisTemplate.delete("register:privateId" + phone);
//        String decrypt = AESUtil.encrypt(rCode);
//        if (!decrypt.equals(decrypt)) {
//            throw new BusinessException("非法请求！");
//        } else {
//            stringRedisTemplate.delete("register:privateId" + phone);
//        }


        // 处理频繁发送
        String redisCode = stringRedisTemplate.opsForValue().get(RedisKey.UPDATE_PASSWORD_CODE_KEY + phone);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(redisCode) && System.currentTimeMillis() - Long.parseLong(redisCode.split("_")[1]) < RedisKey.UPDATE_PASSWORD_CODE_TTL * 6 * 10000) {
            // 调用接口小于60秒间隔不允许重新发送新的验证码
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请勿频繁发送！");
        }
        ifUserSendCode(phone, request);
        // 生成短信验证码
        String code = CommonUtil.getCode();
        // 发送验证码
        sendCodeUtil(phone, code);
        //将验证码存入redis
        stringRedisTemplate.opsForValue().set(RedisKey.UPDATE_PASSWORD_CODE_KEY + phone, code + "_" + System.currentTimeMillis(), RedisKey.UPDATE_PASSWORD_CODE_TTL, TimeUnit.MINUTES);
        log.info(this.getClass().getName()+":noUpdatePassSendCode 修改密码验证码发送成功！本次验证码为：" + code);
    }
    /**
     * 检验修改密码的验证码
     *
     * @param phone
     * @param code
     * @return
     */
    @Override
    public void checkUpdatePassCode(String phone, String code) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(code)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请输入验证码！");
        }
        if (verifyCode) {
            String rCode = stringRedisTemplate.opsForValue().get(RedisKey.UPDATE_PASSWORD_CODE_KEY + phone);
            if (rCode == null) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码已失效，请重新发送！");
            }
            if (!code.equals(rCode.split("_")[0])) {
                throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码错误！");
            }
        }
    }

    /**
     * 修改密码
     *
     * @param newPassword
     */
    @Override
    public void updatePassword(String newPassword) {
//        if(PasswordUtils.password(newPassword)) {
//            throw new BusinessException("密码中必须包含字母、数字、特称字符，至少8个字符，最多20个字符");
//        }
        User user = lambdaQuery().eq(User::getUserId, ThreadLocalUtil.getCurrentUser().getUserId())
                .select(User::getPassword, User::getUserId).one();
        if (user == null) {
            throw new BusinessException(400, "用户不存在！");
        }
        String url = mallConfig.prodPcwp2Url + "/hr/user/changeUserPassword";
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("newPwd", newPassword);
        postParameters.add("userId", ThreadLocalUtil.getCurrentUser().getFarUserId());
        postParameters.add("oldPwd", user.getPassword());
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        headers.add("token", ThreadLocalUtil.getCurrentUser().getToken());
        headers.add("org", "{\"orgId\":\"ad551eff9d03-8efe-2148-9ed8-64781e1e\",\"orgName\":\"四川路桥建设集团股份有限公司\",\"shortCode\":\"SRBC\",\"orgType\":1}");
        headers.add("sysCode", "msp");
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(postParameters, headers);
        R r = restTemplate.postForObject(url, entity, R.class);
        if (r.getCode() == 200) {
            lambdaUpdate().eq(User::getUserId, ThreadLocalUtil.getCurrentUser().getUserId())
                    .set(User::getPassword, newPassword)
                    .set(User::getGmtModified, new Date())
                    .set(User::getLockedState, 0)
                    // 修改密码之后更新数据库密码修改时间
                    .set(User::getPwdChangeDate, new Date())
                    .update();
        } else {
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }

    @Override
    public void updatePassword(String userPhone, String newPassword) {
        User user = lambdaQuery().eq(User::getAccount, userPhone)
                .select(User::getPassword, User::getUserId, User::getInteriorId).one();
        if (user == null) {
            throw new BusinessException(400, "用户不存在！");
        }
        PcwpRes<TokenRes> msp = pcwpService.createExternalToken(userPhone);
        String token = msp.getData().getToken();

        PcwpRes<Void> r = pcwpService.changeUserPassword(user.getInteriorId(), user.getPassword(), newPassword, token);
        if (r.getCode() == 200) {
            lambdaUpdate().eq(User::getUserId, user.getUserId())
                    .set(User::getPassword, newPassword)
                    .set(User::getGmtModified, new Date())
                    .set(User::getLockedState, 0)
                    // 修改密码之后更新数据库密码修改时间
                    .set(User::getPwdChangeDate, new Date())
                    .update();
        } else {
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }

    /**
     * 修改手机号发送验证码
     *
     * @param phone
     * @param privateKeyId
     * @param request
     */
    @Override
    public void updatePhoneSendCode(String phone, String privateKeyId, HttpServletRequest request) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (StringUtils.isEmpty(privateKeyId)) {
            throw new BusinessException("非法请求！");
        }
        String rCode = stringRedisTemplate.opsForValue().get("register:privateId" + phone);
        if (rCode == null) {
            throw new BusinessException("获取失败，请重新获取！");
        }
        log.info(this.getClass().getName()+":updatePhoneSendCode:"+rCode);
        stringRedisTemplate.delete("register:privateId" + phone);
//        String decrypt = AESUtil.encrypt(rCode);
//        if (!decrypt.equals(decrypt)) {
//            throw new BusinessException("非法请求！");
//        } else {
//            stringRedisTemplate.delete("register:privateId" + phone);
//        }

        // 处理频繁发送
        String redisCode = stringRedisTemplate.opsForValue().get(RedisKey.UPDATE_MOBILE_CODE_KEY + phone);
        if (org.apache.commons.lang3.StringUtils.isNotBlank(redisCode) && System.currentTimeMillis() - Long.parseLong(redisCode.split("_")[1]) < RedisKey.UPDATE_MOBILE_CODE_TTL * 6 * 10000) {
            // 调用接口小于60秒间隔不允许重新发送新的验证码
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请勿频繁发送！");
        }
        // 生成短信验证码
        String code = CommonUtil.getCode();
        // 发送验证码
        sendCodeUtil(phone, code);
        //将验证码存入redis
        stringRedisTemplate.opsForValue().set(RedisKey.UPDATE_MOBILE_CODE_KEY + phone, code + "_" + System.currentTimeMillis(), RedisKey.UPDATE_MOBILE_CODE_TTL, TimeUnit.MINUTES);
        log.info("修改手机号验证码发送成功！本次验证码为：" + code);
    }

    /**
     * 检验修改手机的验证码
     *
     * @param phone
     * @param code
     */
    @Override
    public void checkUpdatePhoneCode(String phone, String code) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(code)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请输入验证码！");
        }
        String rCode = stringRedisTemplate.opsForValue().get(RedisKey.UPDATE_MOBILE_CODE_KEY + phone);
        if (rCode == null) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码已失效，请重新发送！");
        }
        if (!code.equals(rCode.split("_")[0])) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码错误！");
        }
    }

    /**
     * 修改手机号
     *
     * @param newPhone
     * @param code
     */
    @Override
    public void updatePhone(String newPhone, String code) {
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(newPhone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        Integer count = lambdaQuery().eq(User::getUserMobile, newPhone).count();
        if (count > 0) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号已注册不能更换！");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(code)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请输入验证码！");
        }
        String rCode = stringRedisTemplate.opsForValue().get(RedisKey.UPDATE_MOBILE_CODE_KEY + newPhone);
        if (rCode == null) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码已失效，请重新发送！");
        }
        if (!code.equals(rCode.split("_")[0])) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码错误！");
        }
        User user = lambdaQuery().eq(User::getUserId, ThreadLocalUtil.getCurrentUser().getUserId())
                .select(User::getInteriorId, User::getUserMobile).one();
        if (user == null) {
            throw new BusinessException(400, "用户不存在！");
        }
        String url = mallConfig.prodPcwp2Url + "//hr/user/changeUserPhone";
        MultiValueMap<String, Object> postParameters = new LinkedMultiValueMap<>();
        postParameters.add("userId", user.getInteriorId());
        postParameters.add("newPhoneNO", newPhone);
        postParameters.add("oldPhoneNO", user.getUserMobile());
        log.warn("修改手机号：原手机：" + user.getUserMobile() + "新手机：" + newPhone);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/x-www-form-urlencoded");
        headers.add("token", ThreadLocalUtil.getCurrentUser().getToken());
        headers.add("org", "{\"orgId\":\"ad551eff9d03-8efe-2148-9ed8-64781e1e\",\"orgName\":\"四川路桥建设集团股份有限公司\",\"shortCode\":\"SRBC\",\"orgType\":1}");
        headers.add("sysCode", "msp");
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(postParameters, headers);
        R r = restTemplate.postForObject(url, entity, R.class);
        if (r.getCode() == 200) {
            lambdaUpdate().eq(User::getUserId, ThreadLocalUtil.getCurrentUser().getUserId())
                    // TODO 外部用户默认账号也是手机号
                    .set(User::getAccount, newPhone)
                    .set(User::getUserMobile, newPhone)
                    .set(User::getGmtModified, new Date()).update();
        } else {
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }

    /**
     * 查询用户分页统计列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getUserCountList(JSONObject jsonObject, LambdaQueryWrapper<User> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        Integer mallType = (Integer) innerMap.get("mallType");
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        if (mallType == 1) {
            q.eq(User::getIsDevice, 1);
        }
        if (mallType == 0) {
            q.eq(User::getIsMaterial, 1);
        }
        q.between(org.apache.commons.lang.StringUtils.isNotEmpty(startCreateDate) && org.apache.commons.lang.StringUtils.isNotEmpty(endCreateDate), User::getGmtCreate, startCreateDate, endCreateDate);
        IPage<User> page = this.page(
                new Query<User>().getPage(jsonObject),
                q
        );
        List<User> records = page.getRecords();
        ArrayList<String> labelTitle = new ArrayList<>();
        ArrayList<Integer> count = new ArrayList<>();

        if (!CollectionUtils.isEmpty(records)) {
            // 统计数量
            QueryWrapper<User> q2 = new QueryWrapper<>();
            q2.between(org.apache.commons.lang.StringUtils.isNotEmpty(startCreateDate) && org.apache.commons.lang.StringUtils.isNotEmpty(endCreateDate), "gmt_create", startCreateDate, endCreateDate);
            q2.select("count(*) as count, is_internal_user as isInternalUser");
            if (mallType == 1) {
                q2.eq("is_device", 1);
            }
            if (mallType == 0) {
                q2.eq("is_material", 1);
            }
            q2.groupBy("is_internal_user");
            List<Map<String, Object>> maps = baseMapper.selectMaps(q2);
            if (CollectionUtils.isEmpty(maps)) {
                records.get(0).setLabelTitle(labelTitle);
                records.get(0).setCount(count);
            } else {
                for (Map<String, Object> map : maps) {
                    Integer isInternalUser = (Integer) map.get("isInternalUser");
                    Long count1 = (Long) map.get("count");
                    // 装备
                    if (isInternalUser != null && isInternalUser == 1) {
                        labelTitle.add("内部用户");
                        count.add(Math.toIntExact(count1));
                        continue;
                    }
                    // 二手装备
                    if (isInternalUser != null && isInternalUser == 0) {
                        labelTitle.add("外部用户");
                        count.add(Math.toIntExact(count1));
                    }
                }
            }
            LambdaQueryChainWrapper<EnterpriseInfo> q3 = enterpriseInfoService.lambdaQuery();
            if (mallType == 1) {
                q3.eq(EnterpriseInfo::getIsDeviceMall, 1);
            }
            if (mallType == 0) {
                q3.eq(EnterpriseInfo::getIsMaterialMall, 1);
            }
            q3.between(org.apache.commons.lang.StringUtils.isNotEmpty(startCreateDate) && org.apache.commons.lang.StringUtils.isNotEmpty(endCreateDate), EnterpriseInfo::getGmtCreate, startCreateDate, endCreateDate);
            Integer count1 = q3.eq(EnterpriseInfo::getIsSupplier, 2).count();
            labelTitle.add("供应商用户");
            count.add(count1);
            records.get(0).setLabelTitle(labelTitle);
            records.get(0).setCount(count);
        }
        return new PageUtils(page);
    }

    /**
     * 根据账号获取用户的信息企业信息店铺信息
     *
     * @param userNumber
     * @param mallType
     * @return
     */
    @Override
    public UserShopEnterpriseInfoVO getUserShopEnterpriseInfoByUserSn(String userNumber, Integer mallType) {
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        UserShopEnterpriseInfoVO vo = new UserShopEnterpriseInfoVO();
        User user = lambdaQuery().eq(User::getUserNumber, userNumber).one();
        if (user != null) {
            vo.setUser(user);
            LambdaQueryChainWrapper<EnterpriseInfo> q = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId());
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                q.eq(EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode());
            } else {
                q.eq(EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode());
            }
            EnterpriseInfo enterpriseInfo = q.one();
            if (enterpriseInfo != null) {
                vo.setEnterpriseInfo(enterpriseInfo);
                // TODO 分库了，是否需要，需要通过远程查询
//                Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId()).eq(Shop::getMallType, mallType).one();
//                vo.setShop(shop);
            }
        }
        return vo;
    }

    /**
     * 供应商登陆
     *
     * @param account
     * @param password
     * @param mallType
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginVO supplierLogin(String account, String password, Integer mallType, HttpServletRequest request) {
        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        Integer userCount = userService.lambdaQuery()
                .and((t) -> t.eq(User::getAccount, account)
                        .or()
                        .eq(User::getUserMobile, account))
                .eq(mallType == 0, User::getMaterialState, 0)
                .eq(mallType == 1, User::getDeviceState, 0).count();
        if (userCount > 0) {
            throw new BusinessException(400, "该用户已被停用，禁止登陆！");
        }
        boolean isPhone = false;
        boolean isHaveInteriorId = false;
        // 校验是不是手机号
        if (!RegexUtils.isPhoneInvalid(account) && password == null) {
            isPhone = true;
        }
        R<Map> r = null;
        HttpHeaders thisHeaders = new HttpHeaders();
        thisHeaders.add("sysCode", "msp");
        HashMap<String, Object> thisMap = new HashMap<>();
        thisMap.put("sysCode", "msp");
        String content = JSON.toJSONString(thisMap);
        HttpEntity<String> thisRequest = new HttpEntity<>(content, thisHeaders);
        if (isPhone) {
            // 如果是手机号
            // 如果当前错误次数大于等于8次拒绝登录
            User userPwd = lambdaQuery().eq(User::getUserMobile, account).one();
            if (userPwd != null && userPwd.getLockedState() == 1) {
                throw new BusinessException("用户已锁定，请联系管理员！！");
            }
            String url = mallConfig.prodPcwp2Url + "/identity/auth/createExternalToken?phoneNo=" + account + "&sysCode=msp";
            r = restTemplate.postForObject(url, thisRequest, R.class);
        } else {
            String url = mallConfig.prodPcwp2Url + "/identity/auth/signin?account="
                    + account + "&password=" + password + "&identityType=5&sysCode=msp";
            r = restTemplate.postForObject(url, null, R.class);
        }
        if (r.getCode() == 200) {
            LoginVO vo = BeanUtils.mapToBean(r.getData(), LoginVO.class);
            vo.setOriginalUserName(vo.getUserName());
            vo.setFarUserId(vo.getUserId());
            vo.setUserNumber(vo.getUserNumber());
            Integer isInterior = 0;
            if (vo.getIsExternal() == null) {
                vo.setIsExternal(1);
                isInterior = 0;
            }
            if (vo.getIsExternal() == 0) {
                isInterior = 1;
            }
            vo.setIsInterior(isInterior);
            // 是内部用户
            if (isInterior == 1) {
                if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId())) {
                    LoginVO loginVO = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId()), LoginVO.class);
                    log.warn("已登陆，直接登陆成功！");
                    return loginVO;
                }
                // 内部都是企业
                vo.setEnterpriseType(1);
                User qUser = userService.lambdaQuery().eq(User::getInteriorId, vo.getUserId()).one();
                String orgUrl = mallConfig.prodPcwp2Url + "/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
//                String orgUrl = "http://pcwp2-api.scrbg.com/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
                R orgR = restTemplate.getForObject(orgUrl, R.class);
                if (orgR.getCode() == 200) {
                    ArrayList orgArr = (ArrayList) orgR.getData();
                    // 第一个就是当前企业
                    if (!CollectionUtils.isEmpty(orgArr)) {
                        Map orgMap = (Map) orgArr.get(0);
                        vo.setOrgInfo(orgMap);
                        // 设置当前机构
                        String orgId = (String) orgMap.get("orgId");
                        String orgName = (String) orgMap.get("orgName");
                        String orgNumber = (String) orgMap.get("shortCode");
                        vo.setOrgId(orgId);
                        vo.setOrgName(orgName);
                        vo.setOrgNumber(orgNumber);
                        HttpHeaders headers = new HttpHeaders();
                        headers.add("token", vo.getToken());
                        headers.add("org", JSONObject.toJSONString(orgMap));
                        if (mallType == 1) {
                            headers.add("sysCode", "egp");
                        }
                        if (mallType == 0) {
                            headers.add("sysCode", "msp");
                        }
//                        System.out.println("当前机构的json：" + JSONObject.toJSONString(orgMap));

                        // 配置角色数据
                        configRoteInfo(mallType, vo, headers);

//                        System.out.println("九宫格菜单：" + menuR.toString());
                        // 本机构以及其下机构列表
                        String orgCUrl = mallConfig.prodPcwp2Url + "/hr/org/getChildrenOrg?orgId=" + orgId
                                + "&orgName=" + orgName;
                        ResponseEntity<R> mapResponseEntity = restTemplateUtils.get(orgCUrl, headers, R.class);
                        R thisAndChild = mapResponseEntity.getBody();
                        if (thisAndChild.getCode() == 200) {
                            ArrayList childList = (ArrayList) thisAndChild.getData();
                            ArrayList<String> orgIds = new ArrayList<>();
                            ArrayList<OrgAndSon> orgAndSons = new ArrayList<>();
                            if (!CollectionUtils.isEmpty(childList)) {
                                for (Object o : childList) {
                                    Map ch = (Map) o;
                                    orgIds.add((String) ch.get("orgId"));
                                    OrgAndSon orgAndSon = new OrgAndSon();
                                    orgAndSon.setOrgId((String) ch.get("orgId"));
                                    orgAndSon.setOrgName((String) ch.get("orgName"));
                                    orgAndSon.setShortCode((String) ch.get("shortCode"));
                                    orgAndSons.add(orgAndSon);
                                }
                            }
                            // 当前机构id以及子机构id
                            vo.setOrgIds(orgIds);
                            vo.setOrgAndSon(orgAndSons);
                        }
//                        System.out.println("查询当前机构以及子机构：" + mapResponseEntity.getBody());
                        // 设置机构列表
                        ArrayList<OrganizationVO> organizationVOS = new ArrayList<>();
                        boolean orgFlag = false;
                        for (Object o : orgArr) {
                            Map map = (Map) o;
                            OrganizationVO organizationVO = new OrganizationVO();
                            organizationVO.setOrgId((String) map.get("orgId"));
                            organizationVO.setOrgName((String) map.get("orgName"));
                            organizationVO.setShortCode((String) map.get("shortCode"));
                            organizationVO.setOrgType((Integer) map.get("orgType"));
//                            Integer isShpAuthority = vo.getIsShpAuthority();
//                            if (isShpAuthority != null && isShpAuthority == 1) {
                            // 物资
                            if (mallType == 0) {
                                if (mallConfig.isBusinessOrg==1){
                                    Boolean flag = systemParamService.getIsBusinessOrg(organizationVO.getOrgId());
                                    if (flag) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }else {
                                    if (mallConfig.isMPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }
                            }
                            // 装备
                            if (mallType == 1) {
                                if (mallConfig.isDPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                    orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                }
                            }
//                            }
                            organizationVOS.add(organizationVO);
                        }
                        if (orgFlag) {
                            vo.setIsPlatformAdmin(1);
                        } else {
                            vo.setIsPlatformAdmin(0);
                        }
                        // 特殊处理账号权限
                        List<String> authArr = new ArrayList<>();
                        AuthArrUtil.addAuth(authArr);
//                        authArr.add("wisesoft");
//                        authArr.add("005718");
//                        authArr.add("srbg5718");
//                        authArr.add("032232");
//                        authArr.add("laizq0119");
                        if (authArr.contains(account)){
                            vo.setIsPlatformAdmin(1);
                        }
                        vo.setOrganizationVOS(organizationVOS);
                    }
                } else {
                    throw new BusinessException(orgR.getCode(), orgR.getMessage());
                }
                if (qUser == null) {
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    if (enterpriseInfo == null) {
                        // 企业不存在
                        EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                        // 企业编号
                        enterpriseInfo1.setCreationTime(new Date());
                        enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                        enterpriseInfo1.setInteriorId(vo.getOrgId());
                        enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                        enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                        enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                        } else {
                            enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                        }
                        enterpriseInfo1.setEnterpriseType(1);
                        enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                        int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                        if (insert != 0) {
                            vo.setLocalOrgId(enterpriseInfo1.getEnterpriseId());
                            vo.setEnterpriseName(enterpriseInfo1.getEnterpriseName());
                            vo.setEnterpriseType(enterpriseInfo1.getEnterpriseType());
                            // 如果用户不存在则保存内部用户数据
                            User user = new User();
                            if (isPhone) {
                                user.setUserMobile(account);
                            } else {
                                user.setAccount(account);
                            }
                            if (!isPhone) {
                                user.setPassword(password);
                            }
                            user.setInteriorId(vo.getUserId());
                            user.setNickName(vo.getUserName());
                            user.setEnterpriseId(enterpriseInfo1.getEnterpriseId());
                            user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                            // 状态
                            user.setIsInternalUser(isInterior);
                            user.setState(PublicEnum.IS_YES.getCode());
                            if (mallType == PublicEnum.MATERIALS.getCode()) {
                                user.setIsMaterial(PublicEnum.IS_YES.getCode());
                            } else {
                                user.setIsDevice(PublicEnum.IS_YES.getCode());
                            }
                            boolean save = userService.save(user);
                            if (save) {
                                vo.setUserId(user.getUserId());
                                vo.setUserName(user.getNickName());
                            }
                        }
                    } else {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        // 企业存在用户不存在
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 如果用户不存在则保存内部用户数据
                        User user = new User();
                        if (isPhone) {
                            user.setUserMobile(account);
                        } else {
                            user.setAccount(account);
                        }
                        if (!isPhone) {
                            user.setPassword(password);
                        }
                        user.setInteriorId(vo.getUserId());
                        user.setNickName(vo.getUserName());
                        user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                        user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                        user.setIsInternalUser(isInterior);
                        user.setState(PublicEnum.IS_YES.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            user.setIsMaterial(PublicEnum.IS_YES.getCode());
                        } else {
                            user.setIsDevice(PublicEnum.IS_YES.getCode());
                        }
                        boolean save = userService.save(user);
                        if (save) {
                            vo.setUserId(user.getUserId());
                            vo.setUserName(user.getNickName());
                        }
                    }
                } else {
                    // 用户存在
                    userIsEnable(vo.getUserNumber());
                    vo.setUserId(qUser.getUserId());
                    vo.setUserName(qUser.getNickName());

                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    if (enterpriseInfo != null) {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 每次登陆修改回当前机构
                        LambdaUpdateChainWrapper<User> setL = userService.lambdaUpdate().eq(User::getUserId, qUser.getUserId())
                                .set(User::getEnterpriseId, enterpriseInfo.getEnterpriseId());
                        // 从token 登陆进来可能不会保存用户账号，如果这里用户但是账号或手机号不存在则需要保存
                        String account1 = qUser.getAccount();
                        String userMobile = qUser.getUserMobile();
                        if (StringUtils.isEmpty(account1) && StringUtils.isEmpty(userMobile)) {
                            // 如果账号手机号都不存在则保存
                            setL.set(isPhone, User::getUserMobile, account);
                            setL.set(!isPhone, User::getAccount, account);
                        }
                        setL.update();
                        if (vo.getIsShpAuthority() == 1) {
                            Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .eq(Shop::getMallType, mallType)
                                    .one();
                            if (shop != null) {
                                vo.setShopId(shop.getShopId());
                                vo.setShopType(shop.getShopType());
                                vo.setShopName(shop.getShopName());
                            }
                        }
                    }
                }
            } else {
                vo.setIsSubmitOrder(1);
                vo.setIsCheck(0);
                vo.setOrgNumber("0");
                vo.setIsPlatformAdmin(0);
                vo.setIsTender(0);
                // 外部用户
                // 外部用户都是手机号登陆
                User userOne = lambdaQuery()
//                        .eq(!isPhone,User::getAccount, account)
                        .eq(User::getUserMobile, account)
                        .eq(User::getIsInternalUser, 0)
                        .one();
                // 用户存在
                if (userOne != null) {
                    if (StringUtils.isEmpty(userOne.getInteriorId())) {
                        isHaveInteriorId = true;
                    }
                    if(userOne.getState() == 0) {
                        throw new BusinessException("用户已被禁用，请联系管理员！！");
                    }
                    vo.setUserId(userOne.getUserId());
                    vo.setUserMobile(userOne.getUserMobile());
                    vo.setUserNumber(userOne.getUserNumber());
                    vo.setFarUserId(userOne.getInteriorId());
                    // 都有电商权限
                    vo.setIsShpAuthority(1);
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getEnterpriseId, userOne.getEnterpriseId()).one();
                    if (enterpriseInfo != null) {
//                        if (enterpriseInfo.getIsSupplier() == 0 || enterpriseInfo.getIsNoSupplierAudit() == 1 || enterpriseInfo.getIsSupplier() == 1) {
//                            throw new BusinessException(400, "账号未通过供应商审核，账号被锁定无法登陆！");
//                        }
                        vo.setSocialCreditCode(enterpriseInfo.getSocialCreditCode());
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        vo.setIsPcwp(enterpriseInfo.getIsPcwp());
                        if (mallType == 0) {
                            Shop shopDate = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .one();
                            if (shopDate != null) {
                                vo.setShopId(shopDate.getShopId());
                                vo.setShopType(shopDate.getShopType());
                                vo.setShopName(shopDate.getShopName());
                            }
                        }
                        if (mallType == 1) {
                            Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
                                    .eq(Shop::getMallType, mallType)
                                    .one();
                            if (shop != null) {
                                vo.setShopId(shop.getShopId());
                                vo.setShopType(shop.getShopType());
                                vo.setShopName(shop.getShopName());
                                if (mallType == 1) {
                                    vo.setIsOtherAuth(JSONObject.parseObject(shop.getIsOtherAuth(), Map.class));
                                }
                            }
                        }
                    } else {
                        UserService service = SpringBeanUtil.getBean(UserService.class);
                        service.handlePwdError(account);
                        throw new BusinessException(400, "用户名或密码错误！");
                    }
                } else {
                    UserService service = SpringBeanUtil.getBean(UserService.class);
                    service.handlePwdError(account);
                    throw new BusinessException(400, "用户名或密码错误！");
                }
            }
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            boolean material = false;
            boolean device = false;
            // 修改
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                material = true;
            } else {
                device = true;
            }
            LambdaUpdateChainWrapper<User> eq = userService.lambdaUpdate()
                    .eq(User::getUserId, vo.getUserId());
            eq.set(isHaveInteriorId, User::getInteriorId, vo.getFarUserId());
            eq.set(material, User::getIsMaterial, PublicEnum.IS_YES.getCode())
                    .set(device, User::getIsDevice, PublicEnum.IS_YES.getCode())
                    .setSql("login_count = login_count + 1")
                    .set(User::getGmtLogin, new Date()).update();

            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            if (vo.getOrgInfo() != null) {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(EnterpriseInfo::getShortCode, vo.getOrgInfo().get("shortCode"))
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            } else {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            }
            if (password == null) {
                userActivityM(vo, "供应商手机号登陆", request);
            } else {
                userActivityM(vo, "供应商账号密码登陆", request);
            }
            return vo;
        } else {
            // 密码错误
            if (r.getMessage().equals("密码错误")) {
                UserService service = SpringBeanUtil.getBean(UserService.class);
                service.handlePwdError(account);
            }
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }

    @Override
    public LoginVO supplierPhoneLogin(String phone, String code, Integer mallType, HttpServletRequest request) {
        // 查询用户是否存在
        Integer count = lambdaQuery().eq(User::getUserMobile, phone).count();
        if (count == 0) {
            log.error("未注册登陆发送短信手机号：" + phone);
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "该用户未注册，请注册后再试！");
        }
        // 校验手机号
        if (RegexUtils.isPhoneInvalid(phone)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "手机号码格式错误！");
        }
        if (org.apache.commons.lang.StringUtils.isBlank(code)) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "请输入验证码！");
        }
        String rCode = stringRedisTemplate.opsForValue().get(RedisKey.LOGIN_CODE_KEY + phone);
        if (rCode == null) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码已失效，请重新发送！");
        }
        if (!code.equals(rCode.split("_")[0])) {
            throw new BusinessException(PublicEnum.RESULT_CODE_400.getCode(), "验证码错误！");
        }
        LoginVO vo = supplierLogin(phone, null, mallType, request);
        return vo;
    }

    @Override
    public boolean getIsSupplier(String socialCreditCode, Integer mallType) {
        if (StringUtils.isEmpty(socialCreditCode) || mallType == null) {
            throw new BusinessException(400, "信用代码或商城类型不能为空！");
        }
        LambdaQueryChainWrapper<EnterpriseInfo> q = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getSocialCreditCode, socialCreditCode)
                .eq(EnterpriseInfo::getIsSupplier, 2);
        if (mallType == 0) {
            q.eq(EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode());
        }
        if (mallType == 1) {
            q.eq(EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode());
        }
        Integer count = q.count();
        if (count == null || count == 0) {
            return false;
        }
        return true;
    }

    /**
     * 批量修改状态
     *
     * @param jsonObject
     * @param request
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchUserState(JSONObject jsonObject, HttpServletRequest request) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        Integer mallType = (Integer) innerMap.get("mallType");
        String token = request.getHeader("token");
        String url = mallConfig.prodPcwp2Url + "/identity/auth/verifyToken?token=" + token;
        HttpHeaders thisHeaders = new HttpHeaders();
        thisHeaders.add("sysCode", "msp");
        HttpEntity<String> thisRequest = new HttpEntity<>(thisHeaders);
        R<Map> r = restTemplate.postForObject(url, thisRequest, R.class);
        if (r.getCode() == 200) {
            Map dataMap = r.getData();
            String id = (String) dataMap.get("userId");
            if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + mallType + "_" + id)) {
                LoginVO vo = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + id), LoginVO.class);
                if (vo.getIsPlatformAdmin() == 1) {
                    List<String> ids = (List<String>) innerMap.get("ids");
                    Integer state = (Integer) innerMap.get("state");
                    boolean update = lambdaUpdate().in(User::getUserId, ids)
                            .set(mallType == 0, User::getMaterialState, state)
                            .set(mallType == 1, User::getDeviceState, state)
                            .set(User::getGmtModified, new Date())
                            .update();
                    if (update) {
                        if (state == 0) {
                            List<User> list = userService.lambdaQuery().in(User::getUserId, ids)
                                    .select(User::getInteriorId).list();
                            if (!CollectionUtils.isEmpty(list) && list.get(0) != null) {
                                for (User user : list) {
                                    stringRedisTemplate.delete(RedisKey.USER_MAP_KEY + mallType + "_" + user.getInteriorId());
                                }
                            }
                        }
                    } else {
                        throw new BusinessException(400, "修改失败！");
                    }
                } else {
                    throw new BusinessException(400, "没有权限！");
                }
            } else {
                throw new BusinessException(400, "登陆过期、请重新登陆！");
            }
        } else {
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }


    @Override
    public List<User> getUserListByids(List<String> ids) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(User::getUserNumber, User::getUserId, User::getRealName, User::getEnterpriseId);
        wrapper.in(User::getUserId, ids);
        List<User> list = list(wrapper);
        return list;
    }

    @Override
    public EnterpriseInfo getSupplierInfo(String socialCreditCode, Integer mallType) {
        if (StringUtils.isEmpty(socialCreditCode) || mallType == null) {
            throw new BusinessException(400, "信用代码或商城类型不能为空！");
        }
        LambdaQueryChainWrapper<EnterpriseInfo> q = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getSocialCreditCode, socialCreditCode)
                .eq(EnterpriseInfo::getIsSupplier, 2);
        if (mallType == 0) {
            q.eq(EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode());
        }
        if (mallType == 1) {
            q.eq(EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode());
        }
        EnterpriseInfo one = enterpriseInfoService.getOne(q);
        return one;
    }

    @Override
    public EnterpriseInfo getAuditState() {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        EnterpriseInfo one = enterpriseInfoService.getAuditState(user.getEnterpriseId());
        return one;
    }

    /**
     * 内部用户登录接口
     *
     * @param account
     * @param password
     * @param mallType
     * @param request
     * @return
     */
    @Override
    public LoginVO interiorLogin(String account, String password, Integer mallType, HttpServletRequest request) {

        if (mallType == null) {
            mallType = mallConfig.mallType;
        }
        Integer userCount = userService.lambdaQuery()
                .and((t) -> t.eq(User::getAccount, account)
                        .or()
                        .eq(User::getUserMobile, account))
                .eq(mallType == 0, User::getMaterialState, 0)
                .eq(mallType == 1, User::getDeviceState, 0).count();
        if (userCount > 0) {
            throw new BusinessException(400, "该用户已被停用，禁止登陆！");
        }
        boolean isPhone = false;
        boolean isHaveInteriorId = false;
        // 校验是不是手机号
        if (!RegexUtils.isPhoneInvalid(account) && password == null) {
            isPhone = true;
        }
        R<Map> r = null;
        HttpHeaders thisHeaders = new HttpHeaders();
        thisHeaders.add("sysCode", "msp");
        HashMap<String, Object> thisMap = new HashMap<>();
        thisMap.put("sysCode", "msp");
        String content = JSON.toJSONString(thisMap);
        HttpEntity<String> thisRequest = new HttpEntity<>(content, thisHeaders);
        if (isPhone) {
            // 如果是手机号
            String url = mallConfig.prodPcwp2Url + "/identity/auth/createExternalToken?phoneNo=" + account + "&sysCode=msp";
            r = restTemplate.postForObject(url, thisRequest, R.class);
        } else {
            String url = mallConfig.prodPcwp2Url + "/identity/auth/signin?account="
                    + account + "&password=" + password + "&identityType=5&sysCode=msp";
            r = restTemplate.postForObject(url, null, R.class);
        }
        if (r.getCode() == 200) {
            LoginVO vo = BeanUtils.mapToBean(r.getData(), LoginVO.class);
            vo.setOriginalUserName(vo.getUserName());
            vo.setFarUserId(vo.getUserId());
            vo.setUserNumber(vo.getUserNumber());
            Integer isInterior = 0;
            if (vo.getIsExternal() == null) {
                vo.setIsExternal(1);
                isInterior = 0;
            }
            if (vo.getIsExternal() == 0) {
                isInterior = 1;
            }
            vo.setIsInterior(isInterior);
            // 是内部用户
            if (isInterior == 1) {
                if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId())) {
                    LoginVO loginVO = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId()), LoginVO.class);
                    log.warn("已登陆，直接登陆成功！");
                    return loginVO;
                }
                // 内部都是企业
                vo.setEnterpriseType(1);
//                // 内部都不是供应商
//                vo.setIsSupplier(0);
                User qUser = userService.lambdaQuery().eq(User::getInteriorId, vo.getUserId()).one();
                String orgUrl = mallConfig.prodPcwp2Url + "/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
//                String orgUrl = "http://pcwp2-api.scrbg.com/hr/org/getOrgByUserId?userId=" + vo.getFarUserId();
                R orgR = restTemplate.getForObject(orgUrl, R.class);
                if (orgR.getCode() == 200) {
                    ArrayList orgArr = (ArrayList) orgR.getData();
                    // 第一个就是当前企业
                    if (!CollectionUtils.isEmpty(orgArr)) {
                        Map orgMap = (Map) orgArr.get(0);
                        vo.setOrgInfo(orgMap);
                        // 设置当前机构
                        String orgId = (String) orgMap.get("orgId");
                        // 是否平台给管理员
//                        if ("ad551eff9d03-8efe-2148-9ed8-64781e1e".equals(orgId)) {
//                            vo.setIsPlatformAdmin(1);
//                        } else {
//                            vo.setIsPlatformAdmin(0);
//                        }
                        String orgName = (String) orgMap.get("orgName");
                        String orgNumber = (String) orgMap.get("shortCode");
                        vo.setOrgId(orgId);
                        vo.setOrgName(orgName);
                        vo.setOrgNumber(orgNumber);
                        HttpHeaders headers = new HttpHeaders();
                        headers.add("token", vo.getToken());
                        headers.add("org", JSONObject.toJSONString(orgMap));
                        if (mallType == 1) {
                            headers.add("sysCode", "egp");
                        }
                        if (mallType == 0) {
                            headers.add("sysCode", "msp");
                        }
//                        System.out.println("当前机构的json：" + JSONObject.toJSONString(orgMap));
                        // 配置角色数据
                        configRoteInfo(mallType, vo, headers);

                        // 本机构以及其下机构列表
                        String orgCUrl = mallConfig.prodPcwp2Url + "/hr/org/getChildrenOrg?orgId=" + orgId
                                + "&orgName=" + orgName;
                        ResponseEntity<R> mapResponseEntity = restTemplateUtils.get(orgCUrl, headers, R.class);
                        R thisAndChild = mapResponseEntity.getBody();
                        if (thisAndChild.getCode() == 200) {
                            ArrayList childList = (ArrayList) thisAndChild.getData();
                            ArrayList<String> orgIds = new ArrayList<>();
                            ArrayList<OrgAndSon> orgAndSons = new ArrayList<>();
                            if (!CollectionUtils.isEmpty(childList)) {
                                for (Object o : childList) {
                                    Map ch = (Map) o;
                                    orgIds.add((String) ch.get("orgId"));
                                    OrgAndSon orgAndSon = new OrgAndSon();
                                    orgAndSon.setOrgId((String) ch.get("orgId"));
                                    orgAndSon.setOrgName((String) ch.get("orgName"));
                                    orgAndSon.setShortCode((String) ch.get("shortCode"));
                                    orgAndSons.add(orgAndSon);
                                }
                            }
                            // 当前机构id以及子机构id
                            vo.setOrgIds(orgIds);
                            vo.setOrgAndSon(orgAndSons);
                        }
//                        System.out.println("查询当前机构以及子机构：" + mapResponseEntity.getBody());
                        // 设置机构列表
                        ArrayList<OrganizationVO> organizationVOS = new ArrayList<>();
                        boolean orgFlag = false;
                        for (Object o : orgArr) {
                            Map map = (Map) o;
                            OrganizationVO organizationVO = new OrganizationVO();
                            organizationVO.setOrgId((String) map.get("orgId"));
                            organizationVO.setOrgName((String) map.get("orgName"));
                            organizationVO.setShortCode((String) map.get("shortCode"));
                            organizationVO.setOrgType((Integer) map.get("orgType"));
//                            Integer isShpAuthority = vo.getIsShpAuthority();
//                            if (isShpAuthority != null && isShpAuthority == 1) {
                            // 物资
                            if (mallType == 0) {
                                if (mallConfig.isBusinessOrg==1){
                                    Boolean flag = systemParamService.getIsBusinessOrg(organizationVO.getOrgId());
                                    if (flag) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }else {
                                    if (mallConfig.isMPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                        orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                    }
                                }
                            }
                            // 装备
                            if (mallType == 1) {
                                if (mallConfig.isDPlatformAdminOrgId.equals(organizationVO.getOrgId())) {
                                    orgFlag = isOrgFlag(mallType, vo, organizationVO);
                                }
                            }
//                            }
                            organizationVOS.add(organizationVO);
                        }
                        if (orgFlag) {
                            vo.setIsPlatformAdmin(1);
                        } else {
                            vo.setIsPlatformAdmin(0);
                        }
                        // 特殊处理账号权限
                        List<String> authArr = new ArrayList<>();
                        AuthArrUtil.addAuth(authArr);
//                        authArr.add("wisesoft");
//                        authArr.add("005718");
//                        authArr.add("srbg5718");
//                        authArr.add("032232");
//                        authArr.add("laizq0119");
                        if (authArr.contains(account)){
                            vo.setIsPlatformAdmin(1);
                        }
                        vo.setOrganizationVOS(organizationVOS);
                    }
                } else {
                    throw new BusinessException(orgR.getCode(), orgR.getMessage());
                }
                if (qUser == null) {
                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    if (enterpriseInfo == null) {
                        // 企业不存在
                        EnterpriseInfo enterpriseInfo1 = new EnterpriseInfo();
                        // 企业编号
                        enterpriseInfo1.setCreationTime(new Date());
                        enterpriseInfo1.setEnterpriseNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_QY.getRemark()));
                        enterpriseInfo1.setInteriorId(vo.getOrgId());
                        enterpriseInfo1.setEnterpriseName(vo.getOrgName());
                        enterpriseInfo1.setState(PublicEnum.IS_YES.getCode());
                        enterpriseInfo1.setIsSupplier(PublicEnum.IS_NO.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            enterpriseInfo1.setIsMaterialMall(PublicEnum.IS_YES.getCode());
                        } else {
                            enterpriseInfo1.setIsDeviceMall(PublicEnum.IS_YES.getCode());
                        }
                        enterpriseInfo1.setEnterpriseType(1);
                        enterpriseInfo1.setEnterpriseBusinessType(PublicEnum.IS_YES.getCode());
                        int insert = enterpriseInfoMapper.insert(enterpriseInfo1);
                        if (insert != 0) {
                            vo.setLocalOrgId(enterpriseInfo1.getEnterpriseId());
                            vo.setEnterpriseName(enterpriseInfo1.getEnterpriseName());
                            vo.setEnterpriseType(enterpriseInfo1.getEnterpriseType());
                            // 如果用户不存在则保存内部用户数据
                            User user = new User();
                            if (isPhone) {
                                user.setUserMobile(account);
                            } else {
                                user.setAccount(account);
                            }
                            if (!isPhone) {
                                user.setPassword(password);
                            }
                            user.setInteriorId(vo.getUserId());
                            user.setNickName(vo.getUserName());
                            user.setEnterpriseId(enterpriseInfo1.getEnterpriseId());
                            user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                            // 状态
                            user.setIsInternalUser(isInterior);
                            user.setState(PublicEnum.IS_YES.getCode());
                            if (mallType == PublicEnum.MATERIALS.getCode()) {
                                user.setIsMaterial(PublicEnum.IS_YES.getCode());
                            } else {
                                user.setIsDevice(PublicEnum.IS_YES.getCode());
                            }
                            boolean save = userService.save(user);
                            if (save) {
                                vo.setUserId(user.getUserId());
                                vo.setUserName(user.getNickName());
                            }
                        }
                    } else {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        // 企业存在用户不存在
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 如果用户不存在则保存内部用户数据
                        User user = new User();
                        if (isPhone) {
                            user.setUserMobile(account);
                        } else {
                            user.setAccount(account);
                        }
                        if (!isPhone) {
                            user.setPassword(password);
                        }
                        user.setInteriorId(vo.getUserId());
                        user.setNickName(vo.getUserName());
                        user.setEnterpriseId(enterpriseInfo.getEnterpriseId());
                        user.setUserNumber(CodeUtil.getCodeByPrefix(CodeEnum.CODE_YH.getRemark()));
                        user.setIsInternalUser(isInterior);
                        user.setState(PublicEnum.IS_YES.getCode());
                        if (mallType == PublicEnum.MATERIALS.getCode()) {
                            user.setIsMaterial(PublicEnum.IS_YES.getCode());
                        } else {
                            user.setIsDevice(PublicEnum.IS_YES.getCode());
                        }
                        boolean save = userService.save(user);
                        if (save) {
                            vo.setUserId(user.getUserId());
                            vo.setUserName(user.getNickName());
                        }
                    }
                } else {
                    // 用户存在
                    userIsEnable(vo.getUserNumber());
                    vo.setUserId(qUser.getUserId());
                    vo.setUserName(qUser.getNickName());

                    EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery()
                            .eq(EnterpriseInfo::getInteriorId, vo.getOrgId()).one();
                    if (enterpriseInfo != null) {
                        if (enterpriseInfo.getIsSupplier() != null && enterpriseInfo.getIsSupplier() == 2) {
                            vo.setIsSupplier(1);
                        } else {
                            vo.setIsSupplier(0);
                        }
                        vo.setLocalOrgId(enterpriseInfo.getEnterpriseId());
                        vo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                        vo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                        // 每次登陆修改回当前机构
                        LambdaUpdateChainWrapper<User> setL = userService.lambdaUpdate().eq(User::getUserId, qUser.getUserId())
                                .set(User::getEnterpriseId, enterpriseInfo.getEnterpriseId());
                        // 从token 登陆进来可能不会保存用户账号，如果这里用户但是账号或手机号不存在则需要保存
                        String account1 = qUser.getAccount();
                        String userMobile = qUser.getUserMobile();
                        if (StringUtils.isEmpty(account1) && StringUtils.isEmpty(userMobile)) {
                            // 如果账号手机号都不存在则保存
                            setL.set(isPhone, User::getUserMobile, account);
                            setL.set(!isPhone, User::getAccount, account);
                        }
                        setL.update();

//                        if (vo.getIsShpAuthority() == 1) {
//                            if (mallType == 0) {
//                                Shop shopDate = shopService.lambdaQuery().eq(Shop::getEnterpriseId,vo.getLocalOrgId())
//                                        .one();
//                                if (shopDate != null) {
//                                    vo.setShopId(shopDate.getShopId());
//                                    vo.setShopType(shopDate.getShopType());
//                                }
//                            }
//                            if (mallType == 1) {
//                                Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
//                                        .eq(Shop::getMallType, mallType)
//                                        .one();
//                                if (shop != null) {
//                                    vo.setShopId(shop.getShopId());
//                                    vo.setShopType(shop.getShopType());
//                                }
//                            }
//                        }
//                        if (vo.getIsShpAuthority() == 1) {
//                            Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, vo.getLocalOrgId())
//                                    .eq(Shop::getMallType, mallType)
//                                    .one();
//                            if (shop != null) {
//                                vo.setShopId(shop.getShopId());
//                                vo.setShopType(shop.getShopType());
//                            }
//                        }
                    }
                }
            } else {
                // 外部用户直接报错
                throw new BusinessException(500, "该用户不是内部用户！");
            }
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            boolean material = false;
            boolean device = false;
            // 修改
            if (mallType == PublicEnum.MATERIALS.getCode()) {
                material = true;
            } else {
                device = true;
            }
            LambdaUpdateChainWrapper<User> eq = userService.lambdaUpdate()
                    .eq(User::getUserId, vo.getUserId());
            eq.set(isHaveInteriorId, User::getInteriorId, vo.getFarUserId());
            eq.set(material, User::getIsMaterial, PublicEnum.IS_YES.getCode())
                    .set(device, User::getIsDevice, PublicEnum.IS_YES.getCode())
                    .setSql("login_count = login_count + 1")
                    .set(User::getGmtLogin, new Date()).update();

            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + mallType + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);
            if (vo.getOrgInfo() != null) {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(EnterpriseInfo::getShortCode, vo.getOrgInfo().get("shortCode"))
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            } else {
                enterpriseInfoService.lambdaUpdate().eq(EnterpriseInfo::getEnterpriseId, vo.getLocalOrgId())
                        .set(material, EnterpriseInfo::getIsMaterialMall, PublicEnum.IS_YES.getCode())
                        .set(device, EnterpriseInfo::getIsDeviceMall, PublicEnum.IS_YES.getCode()).update();
            }
            userActivityM(vo, "内部用户登陆", request);
            return vo;
        } else {
            throw new BusinessException(r.getCode(), r.getMessage());
        }
    }


    @Override
    @GlobalTransactional
    public void createlasjldf(DsaffDTO dto) {
        File file = new File();
        file.setName("lsdjflajljflasjfja");
        fileService.save(file);


        if (1 == 1) {
            throw new BusinessException("djlfajl");
        }
    }

    @Override
    public LoginVO handleFirstLogin(LoginVO vo) {
        if (vo.getIsInterior() == 1) {
            // 内部用户
            vo.setFirstLogin(1);
            String userId = vo.getUserId();
            String userNumber = vo.getUserNumber();
            User one = super.lambdaQuery().eq(User::getUserId, userId).one();
            if (one.getFirstLogin() == 0) {
                vo.setFirstLogin(0);
            }
            boolean update = super.lambdaUpdate().eq(User::getUserId, userId).set(User::getFirstLogin, 0)
                    .set(User::getOutUserNumber,userNumber)
                    .update();
        }
        return vo;
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Map getLockingByPhone(String phone) {
        if (org.apache.commons.lang.StringUtils.isBlank(phone)) {
            throw new BusinessException("手机号不可为空");
        }
//        this.handlePwdExpire(phone);
        Map<String, Object> res = new HashMap<>(4);
        // 查询
        User user = lambdaQuery().eq(User::getUserMobile, phone).one();
        LocalDate now = LocalDate.now();
        if (!ObjectUtils.isEmpty(user)) {
            if (user.getLastFailedLoginTime() != null && (ChronoUnit.DAYS.between(user.getLastFailedLoginTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), now) >= 1)) {
                user.setLockedState(0);
                user.setPasswordErrTimes(0);
                user.setLockCause(null);
            }
            updateById(user);
            if (!ObjectUtils.isEmpty(user)) {
                res.put("state", user.getLockedState());
                res.put("count", user.getPasswordErrTimes());
                res.put("cause", user.getLockCause());
                res.put("pwdChangeDate", user.getPwdChangeDate());
            }
        }
        return res;
    }

    @Override
    public void handlePwdError(String phone) {
        if (org.apache.commons.lang.StringUtils.isBlank(phone)) {
            throw new BusinessException("手机号不可为空");
        }

        LocalDate now = LocalDate.now();
        User user = lambdaQuery().eq(User::getUserMobile, phone).one();
        // 如果上次错误时间离此时大于24小时则重置错误次数
        if (!ObjectUtils.isEmpty(user)) {
            if (user.getLastFailedLoginTime() != null && (ChronoUnit.DAYS.between(user.getLastFailedLoginTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(), now) >= 1)) {
                user.setLockedState(0);
                user.setPasswordErrTimes(0);
                user.setLockCause(null);
            }
            user.setPasswordErrTimes(user.getPasswordErrTimes() + 1);
            log.warn("密码错误:" + phone + "次数递增结果：" + user.getPasswordErrTimes());
            if (user.getPasswordErrTimes() >= 8) {
                user.setLockedState(1);
                user.setLockCause("密码错误次数超过最大次数");
            }
            lambdaUpdate()
                    .eq(User::getUserMobile, phone)
                    .set(User::getLockedState, user.getLockedState())
                    .set(User::getPasswordErrTimes, user.getPasswordErrTimes())
                    .set(User::getLastFailedLoginTime, new Date())
                    .set(User::getLockCause, user.getLockCause())
                    .set(User::getLastFailedLoginTime, new Date())
                    .update();
        }

    }

    @Override
    public void handlePwdExpire(String phone) {
        if (org.apache.commons.lang.StringUtils.isBlank(phone)) {
            throw new BusinessException("手机号不可为空");
        }
        LocalDate now = LocalDate.now();
        User user = lambdaQuery().eq(User::getUserMobile, phone).one();
        // 如果上次修改日期为空(初始化) 则重置为当前时间
        if (user != null && user.getPwdChangeDate() == null) {
            user.setPwdChangeDate(new Date());
        }
        if (!ObjectUtils.isEmpty(user)) {
            LocalDate date = user.getPwdChangeDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (date.plusDays(90).compareTo(now) <= 0) {
                // 超过三十天未修改密码
                user.setLockedState(1);
                user.setLockCause("超过90天未修改密码");
            }
            lambdaUpdate().eq(User::getUserMobile, phone)
                    .set(User::getPwdChangeDate, user.getPwdChangeDate())
                    .set(User::getLockedState, user.getLockedState())
                    .set(User::getLockCause, user.getLockCause()).update();

        }
        //if (dat)
    }

    @Override
    public Map handleExpireing(String phone) {
        if (org.apache.commons.lang.StringUtils.isBlank(phone)) {
            throw new BusinessException("手机号不可为空");
        }
        User user = lambdaQuery().eq(User::getUserMobile, phone).one();
        LocalDate now = LocalDate.now();
        Map<String, Object> map = new HashMap<>(1);
        if (!ObjectUtils.isEmpty(user) && user.getPwdChangeDate() != null) {
            LocalDate endDate = user.getPwdChangeDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate().plusDays(30);
            long between = ChronoUnit.DAYS.between(endDate, now);
            if (between >= -7 && between <= 0) {
                // 密码到期时间
                map.put("endTime", endDate);
                map.put("state", 1);
                return map;
            }
            if (between > 0) {
                // 密码已过期
                map.put("state", 2);
                return map;
            } else {
                map.put("state", 0);
            }
        }
        return map;

    }

    @Override
    public void handleuccessLogin(String phone) {
        lambdaUpdate().eq(User::getUserMobile, phone).set(User::getLockedState, 0)
                .set(User::getPasswordErrTimes, 0).update();
    }

    @Override
    public ResponseBody thirdLogin(LoginUser user, HttpServletRequest request) {
        log.warn("第三方登陆：" + user.toString());
        LoginVO vo = SpringBeanUtil.getBean(UserService.class).login(user.getUserName(), user.getPassword(), 0, request);
        ResponseBody res = new ResponseBody();
        org.springframework.beans.BeanUtils.copyProperties(vo, res);
        return res;
    }

    @Override
    public UserInfo verifyToken() {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        if (user == null) {
            throw new BusinessException(400, "登陆过期，请重新登陆！");
        }
        UserInfo userInfo = new UserInfo();
        // 取redis的数据
        if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + 0 + "_" + user.getFarUserId())) {
            String userJson = stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + 0 + "_" + user.getFarUserId());
            LoginVO loginVO = JSON.parseObject(userJson, LoginVO.class);
            org.springframework.beans.BeanUtils.copyProperties(loginVO, userInfo);
            // 根据企业类型修改数据
            // 内部用户和外部用户
            EnterpriseInfo enterpriseInfo = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, user.getEnterpriseId()).one();
            if (enterpriseInfo.getCreationTime() != null) {
                userInfo.setCreationTime(enterpriseInfo.getCreationTime());
            }
            if (loginVO.getIsInterior() == 1) {
                // 内部用户只有机构
                userInfo.setEnterpriseType(2);
                // 获取机构名称集合
                userInfo.setOrgList(loginVO.getOrganizationVOS().stream().map(org -> {
                    Map<String, Object> map = new HashMap<>(1);
                    map.put("orgId", org.getOrgId());
                    map.put("orgName", org.getOrgName());
                    return map;
                }).collect(Collectors.toList()));
            } else {
                // 外部
                if (enterpriseInfo != null) {
                    userInfo.setEnterpriseName(enterpriseInfo.getEnterpriseName());
                    userInfo.setEnterpriseType(enterpriseInfo.getEnterpriseType());
                    // 0个体户 1 企业
                    if (enterpriseInfo.getEnterpriseType() == 0) {
                        // 经营者
                        userInfo.setLegalRepresentative(enterpriseInfo.getOperator());
                        userInfo.setPlaceOfBusiness(enterpriseInfo.getPlaceOfBusiness());
                    }
                    // 企业
                    if (enterpriseInfo.getEnterpriseType() == 1) {
                        userInfo.setLegalRepresentative(enterpriseInfo.getLegalRepresentative());
                        userInfo.setPlaceOfBusiness(enterpriseInfo.getDetailedAddress());

                    }

                }

            }
        } else {
            throw new BusinessException(400, "登陆过期，请重新登陆！");
        }
        return userInfo;
    }


    // 获取子菜单
    public void getMenuLists(Map<String,Object> mapSysMenu, List<SysMenu> menuListByRoleNames,String key){
        List<String> list = new ArrayList<>();
        for (SysMenu s1 : menuListByRoleNames) {
            for (SysMenu s2 :  s1.getChildren()) {
                for (SysMenu s3 : s2.getChildren()) {
                    list.add(s3.getCode());
                }
            }
        }
        mapSysMenu.put(key,list);
    }
    /**
     * 添加菜单
     * @param vo
     */
    @Override
    public void addUserMenu(LoginVO vo) {
        if(mallConfig.isLoginAuthQuery == 1) {
            List<MallRole> roleMenuListByRoleNames = sysRoleService.getRoleMenuListByRoleNames(vo.getRoles());
            vo.setMallRoles(roleMenuListByRoleNames);
            List<MallRole> roleMenuList = sysUserRoleMapper.getRoleMenuListByRoleNames
                    (vo.getUserNumber(),vo.getOrgNumber());
            Set<String> permissionList = sysUserRoleMapper.getPermissionListByRoleNames(vo.getUserNumber(),vo.getOrgNumber());
            vo.setPermissions(permissionList);
            vo.setMallRoleList(roleMenuList);
            if (vo.getRoles() == null) {
                vo.setRoles(new ArrayList<String>());
            }
            List<String> roles = vo.getRoles();
            Map<String,Object> mapSysMenu = new HashMap<>();
            String enterpriseName = vo.getEnterpriseName();
            String shopId = vo.getShopId();
            if(!StringUtils.isEmpty(shopId)) {
                Integer count = shopService.lambdaQuery().eq(Shop::getShopId, shopId)
                        .eq(Shop::getAuditStatus, 1).count();
                if(count > 0) {
                    roles.add(RoleEnum.ROLE_35.getName());
                }
            }
            // 自营
            if(!StringUtils.isEmpty(enterpriseName) && ("四川路桥建设集团股份有限公司物资分公司".equals(enterpriseName)||"四川路桥建设集团物资有限责任公司".equals(enterpriseName)) && vo.getIsShpAuthority() == 1) {
                roles.add(RoleEnum.ROLE_31.getName());
                List<SysMenu> menuListByRoleNames = sysRoleService.getMenuListByRoleNames(roles, 4);
                getMenuLists(mapSysMenu,menuListByRoleNames,"oneselfShopManageMenu");
                mapSysMenu.put("oneselfShopManage",menuListByRoleNames);
            }else {
                // 普通供应商
                if (vo.getIsSupplier() != null && vo.getIsSupplier() == 1) {
                    roles.add(RoleEnum.ROLE_30.getName());
                    // 二级共供应商
                    Integer count = shopSupplierReleService.lambdaQuery()
                            .eq(ShopSupplierRele::getSupplierId, vo.getLocalOrgId())
                            .count();
                    if(count > 0) {
                        roles.add(RoleEnum.ROLE_32.getName());
                    }
                    // 二级临购供应商
                    Integer count1 = shopSupplierReleService.lambdaQuery()
                            .eq(ShopSupplierRele::getSupplierId, vo.getLocalOrgId())
                            .eq(ShopSupplierRele::getPermissionsCommodities, "1")
                            .count();
                    if(count1 > 0) {
                        roles.add(RoleEnum.ROLE_36.getName());
                    }
                    // 二级零星供应商
                    Integer count2 = shopSupplierReleService.lambdaQuery()
                            .eq(ShopSupplierRele::getSupplierId, vo.getLocalOrgId())
                            .eq(ShopSupplierRele::getPermissionsLowValue, "1")
                            .count();
                    if(count2 > 0) {
                        roles.add(RoleEnum.ROLE_37.getName());
                    }
                    List<SysMenu> menuListByRoleNames = sysRoleService.getMenuListByRoleNames(roles, 2);
                    getMenuLists(mapSysMenu,menuListByRoleNames,"supplierManageMenu");
                    mapSysMenu.put("supplierManage",menuListByRoleNames);
                }
            }


            if(vo.getRoles().contains("物资采购平台管理权限")) {
                roles.add(RoleEnum.ROLE_13.getName());
                List<SysMenu> menuListByRoleNames = sysRoleService.getMenuListByRoleNames(roles, 1);
                mapSysMenu.put("platformManage",menuListByRoleNames);
            }

            if(vo.getRoles().contains("物资采购平台履约系统")) {
//                roles.add(RoleEnum.ROLE_5.getName());
                List<SysMenu> menuListByRoleNames = sysRoleService.getMenuListByRoleNames(roles, 3);
                getMenuLists(mapSysMenu,menuListByRoleNames,"performanceManageMenu");
                mapSysMenu.put("performanceManage",menuListByRoleNames);
            }
            vo.setMapSysMenu(mapSysMenu);

            // 更新redis
            stringRedisTemplate.opsForValue()
                    .set(RedisKey.USER_MAP_KEY + 0 + "_" + vo.getFarUserId(), JSON.toJSONString(vo), mallConfig.loginOutTime, TimeUnit.MINUTES);

        }
    }
    private void userIsEnable(String userNumber){
        Integer state = sysUserMapper.getUserState(userNumber);
        if(null != state && state == 0){
            throw new BusinessException("用户已被禁用，请联系管理员！！");
        }
    }
}
