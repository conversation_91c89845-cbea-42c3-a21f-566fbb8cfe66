package scrbg.meplat.mall.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @描述：
 * @作者: sund
 * @日期: 2023-01-31
 */
@ApiModel(value="订单退货")
@Data
@TableName("order_return")
public class OrderReturn extends MustBaseEntity implements Serializable {

private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "退货id")
    private String orderReturnId;

    @ApiModelProperty(value = "订单id")

    private String orderId;
    @ApiModelProperty(value = "订单id(二级)")

    private String otherOrderId;
    @ApiModelProperty(value = "订单编号(二级)")

    private String otherOrderSn;

    @ApiModelProperty(value = "产品名称（多个产品用,隔开）")

    private String untitled;

    @ApiModelProperty(value = "供应商id（本地机构id）")

    private String supplierId;
    @ApiModelProperty(value = "供应商名称（本地机构id）")
    private String supplierName;
    @ApiModelProperty(value = "企业Id（卖方 被退货公司id）")
    private String enterpriseId;
    @ApiModelProperty(value = "企业名称（卖方 被退货公司id）")
    private String enterpriseName;


    @ApiModelProperty(value = "订单编号")

    private String orderSn;

    @ApiModelProperty(value = "退货编号")

    private String orderReturnNo;

    @ApiModelProperty(value = "退货原因（1 ：7天无理由退货 2不想要了 3.物流太慢）")

    private String submitReason;


    @ApiModelProperty(value = "退货人id")

    private String userId;


    @ApiModelProperty(value = "来源类型   1合同  2 计划(零星采购)，6 大宗临购；7：周转材料；")

    private Integer sourceType;
    @ApiModelProperty(value = "清单类型（1浮动价格2固定价格）")

    private Integer billType;

    @ApiModelProperty(value = "退货方式（1上门取件 2快递至卖家）")

    private Integer returnMethod;

    @ApiModelProperty(value = "退货状态 1:已申请 2:退货中 3:退款成功 4:退货失败")

    private Integer state;


    @ApiModelProperty(value = "物流单号")

    private String deliveryFlowId;

    @ApiModelProperty(value = "物流公司")

    private String logisticsCompany;


    @ApiModelProperty(value = "配送方式1(上门取件 2快递至卖家）")

    private String deliveryType;


    @ApiModelProperty(value = "退货成功时间")

    private Date flishTime;


    @ApiModelProperty(value = "收货人姓名")

    private String receiverName;
    @ApiModelProperty(value = "发货人姓名")

    private String sendName;


    @ApiModelProperty(value = "发货人电话")

    private String sendMobile;
    @ApiModelProperty(value = "收货人电话")

    private String receiverMobile;


    @ApiModelProperty(value = "收货人地址")

    private String receiverAddress;
    @ApiModelProperty(value = "发货人地址")

    private String sendAddress;
    @ApiModelProperty(value  ="详细地址")
    private String address;


    @ApiModelProperty(value = "发货时间")

    private Date gmtCreate;
    @ApiModelProperty(value = "发货供应商企业id")

    private String shipEnterpriseId;
    @ApiModelProperty(value = "发货供应商企业名称")

    private String shipEnterpriseName;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal otherNoRateAmount;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal otherRateAmount;

    @ApiModelProperty(value = "商品总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "退货类型  1外部退货  0内部退货")
    private Integer isOut;

    @ApiModelProperty(value = "不含税总金额")
    private BigDecimal noRateAmount;

    @ApiModelProperty(value = "含税总金额")
    private BigDecimal rateAmount;
    @ApiModelProperty(value = "订单类别（1普通订单2多供方订单3已拆分子订单） //1-2  自营店退货   3 二级供应商退货")

    private Integer orderClass;

    @ApiModelProperty(value = "商铺名称")
    private String shopName;

    @ApiModelProperty(value = "商铺Id")
    private String shopId;


    @ApiModelProperty(value = "商铺Id")
    private String outKeyId;



    @TableField(exist = false)
    @ApiModelProperty(value = "退货单项")
    private List<OrderReturnItem> orderReturnItems;
//    @Override
//    protected Object clone() throws CloneNotSupportedException {
//        OrderReturn orderReturn=null;
//        try {
//            orderReturn= (OrderReturn) super.clone();
//        }catch (CloneNotSupportedException.)
//        return super.clone();
//    }


    @ApiModelProperty(value = "商品类型")
    @TableField(exist = false)
    private Integer productType;
}
