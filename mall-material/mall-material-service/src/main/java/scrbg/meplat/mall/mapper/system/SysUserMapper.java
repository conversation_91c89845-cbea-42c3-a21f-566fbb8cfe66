package scrbg.meplat.mall.mapper.system;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.system.SysUser;

@Mapper
@Repository
public interface SysUserMapper extends BaseMapper<SysUser> {
    void updateUserState(@Param("userId") String userId,@Param("state") Integer state);
    Integer getUserState(@Param("userId") String userId);

}
