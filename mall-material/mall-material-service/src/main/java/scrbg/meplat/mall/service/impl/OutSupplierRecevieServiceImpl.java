package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.generator.config.IFileCreate;
import com.scrbg.common.utils.R;
import lombok.Data;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.controller.ProductCategoryController;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.pcwpApi.Pcwp1Servise;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.vo.ship.SubmitOrderShipDtl;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Log4j2
public class OutSupplierRecevieServiceImpl implements OutSupplierRecevieService {
    @Autowired
    private InterfaceLogsService interfaceLogsService;
    @Autowired
    OrderSelectPlanService orderSelectPlanService;
    @Autowired
    OrdersService ordersService;

    @Autowired
    OrderItemService orderItemService;

    @Autowired
    ProductService productService;
    @Autowired
    ProductCategoryService productCategoryService;
    @Autowired
    OrderShipService orderShipService;
    @Autowired
    OrderShipDtlService orderShipDtlService;
    @Autowired
    MallConfig mallConfig;
    @Autowired
    DeviceIpServer deviceIpServer;
    @Autowired
    UserService userService;
    @Autowired
    private RestTemplateUtils restTemplateUtils;
    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    private Pcwp1Servise pcwp1Servise;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;




    @Override
    public R<String> confirmShipUrl2(OrderShip orderShip, String idStr, StringBuilder stringBuilder) {
        if(7 == orderShip.getSourceType()){
            return confirmShipUrl3(orderShip, idStr, stringBuilder);
        }
        Map<String, Object> request = new HashMap<>();
        Map<String, Object> data = getOrderShip(orderShip);
// 设置params参数
        request.put("data", data);
        request.put("keyId", idStr);
        String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.SAVE_SITE_RECEIVING;
        String content = JSON.toJSONString(request);
        log.info("收料推送：" + content);
        stringBuilder.append(content);
        String planNo  =null;
        R<String> r = null;
        LogUtil.writeInfoLog(idStr, "confirmShipUrl", orderShip, request, null, OutSupplierRecevieServiceImpl.class);
        try {
            r = restTemplateUtils.postPCWP2(url, request);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "confirmShipUrl", orderShip, request, null, e.getMessage(), OutSupplierRecevieServiceImpl.class);
            //throw new BusinessException("【远程接口异常】" + e.getMessage());
            if (e.getMessage().contains("Read timed out")){
                //获取超时计划的计划编号
                planNo = pcwp1Servise.pcwp1VerifyOrderShip(mallConfig.prodPcwp2Url02, idStr);
                if (org.springframework.util.StringUtils.isEmpty(planNo)){
                    LogUtil.writeErrorLog(idStr, "confirmShipUrl", orderShip, request, null, e.getMessage(), OutSupplierRecevieServiceImpl.class);
                    log.error("推送计划报错：" + e.getMessage());
                    throw new BusinessException("【远程异常】：" + e.getMessage());
                }
            }else {
                LogUtil.writeErrorLog(idStr, "confirmShipUrl", orderShip, request, null, e.getMessage(), OutSupplierRecevieServiceImpl.class);
                log.error("推送收料报错：" + e.getMessage());
                throw new BusinessException("【远程异常】：" + e.getMessage());
            }

        }
        if (r.getCode() != 200 && r != null) {
            if (r.getMessage().contains("Read timed out")){
                //获取超时计划的计划编号
                planNo = pcwp1Servise.pcwp1VerifyOrderShip(mallConfig.prodPcwp2Url02, idStr);
                if (org.springframework.util.StringUtils.isEmpty(planNo)){
                    LogUtil.writeErrorLog(idStr, "confirmShipUrl2", orderShip, request, r, r.getMessage(), OutSupplierRecevieServiceImpl.class);
                    log.error("【远程接口异常】收料推送返回报错：收料单推送失败" + r.getMessage());
                    throw new BusinessException("【远程接口异常】" +  r.getMessage());
                }
            }else {
                LogUtil.writeErrorLog(idStr, "confirmShipUrl2", orderShip, request, r, r.getMessage(), OutSupplierRecevieServiceImpl.class);
                log.error("【远程接口异常】收料推送返回报错：收料单推送失败" + r.getMessage());
                throw new BusinessException("【远程接口异常】" +  r.getMessage());
            }
        }
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OutSupplierRecevieServiceImpl.class.getName());
        iLog.setMethodName("confirmShipUrl");
        iLog.setLocalArguments(JSON.toJSONString(orderShip));
        iLog.setFarArguments(content);
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setResult(JSON.toJSONString(r));
        interfaceLogsService.create(iLog);
        return r;
    }

    private R<String> confirmShipUrl3(OrderShip orderShip, String idStr, StringBuilder stringBuilder) {
        Map<String,Object> request = assebmblyMap(orderShip,idStr);
        String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.SAVE_SITE_RECEIVING_MATERIALS;
        String content = JSON.toJSONString(request);
        log.info("周材收料推送：" + content);
        stringBuilder.append(content);
        String planNo  =null;
        R<String> r = null;
        LogUtil.writeInfoLog(idStr, "confirmShipUrl",orderShip, request, null, OutSupplierRecevieServiceImpl.class);
        try {
            r = restTemplateUtils.postPCWP2(url, request);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "confirmShipUrl", orderShip, request, null, e.getMessage(), OutSupplierRecevieServiceImpl.class);
            //throw new BusinessException("【远程接口异常】" + e.getMessage());
            if (e.getMessage().contains("Read timed out")){
                //获取超时计划的计划编号
                planNo = pcwp1Servise.pcwp1VerifyOrderShip(mallConfig.prodPcwp2Url02, idStr);
                if (org.springframework.util.StringUtils.isEmpty(planNo)){
                    LogUtil.writeErrorLog(idStr, "confirmShipUrl", orderShip, request, null, e.getMessage(), OutSupplierRecevieServiceImpl.class);
                    log.error("推送计划报错：" + e.getMessage());
                    throw new BusinessException("【远程异常】：" + e.getMessage());
                }
            }else {
                LogUtil.writeErrorLog(idStr, "confirmShipUrl", orderShip, request, null, e.getMessage(), OutSupplierRecevieServiceImpl.class);
                log.error("推送周材收料报错：" + e.getMessage());
                throw new BusinessException("【远程异常】：" + e.getMessage());
            }

        }
        if (r.getCode() != 200 && r != null) {
            if (r.getMessage().contains("Read timed out")){
                //获取超时计划的计划编号
                planNo = pcwp1Servise.pcwp1VerifyOrderShip(mallConfig.prodPcwp2Url02, idStr);
                if (org.springframework.util.StringUtils.isEmpty(planNo)){
                    LogUtil.writeErrorLog(idStr, "confirmShipUrl2", orderShip, request, r, r.getMessage(), OutSupplierRecevieServiceImpl.class);
                    log.error("【远程接口异常】收料推送返回报错：收料单推送失败" + r.getMessage());
                    throw new BusinessException("【远程接口异常】" +  r.getMessage());
                }
            }else {
                LogUtil.writeErrorLog(idStr, "confirmShipUrl2", orderShip, request, r, r.getMessage(), OutSupplierRecevieServiceImpl.class);
                log.error("【远程接口异常】收料推送返回报错：收料单推送失败" + r.getMessage());
                throw new BusinessException("【远程接口异常】" +  r.getMessage());
            }
        }
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OutSupplierRecevieServiceImpl.class.getName());
        iLog.setMethodName("confirmShipUrl");
        iLog.setLocalArguments(JSON.toJSONString(orderShip));
        iLog.setFarArguments(content);
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setResult(JSON.toJSONString(r));
        interfaceLogsService.create(iLog);
        return r;
    }

    private Map<String,Object> assebmblyMap(OrderShip orderShip,String idStr) {
        Map<String,Object> map = new HashMap<>();
        map.put("keyId",idStr);
        map.put("orgId",orderShip.getOrgId());
        Map<String,Object> warehousingDto = new HashMap<>();
        warehousingDto.put("billId",orderShip.getBillId());
        warehousingDto.put("billNo",orderShip.getBillSn());
        Orders order = ordersService.getById(orderShip.getOrderId());

        warehousingDto.put("businessTypeKey",7);
        warehousingDto.put("businessTypeValue",null);
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        warehousingDto.put("orgId", enterpriseInfo.getInteriorId());
        warehousingDto.put("orgName", enterpriseInfo.getEnterpriseName());
        warehousingDto.put("outerType",null);
        User byId = userService.getById(order.getFounderId());
        warehousingDto.put("purchaseId", byId.getInteriorId());
        warehousingDto.put("purchaseName", order.getFounderName());
        warehousingDto.put("receivingPersonnelId",ThreadLocalUtil.getCurrentUser().getUserId());
        warehousingDto.put("receivingPersonnelName",ThreadLocalUtil.getCurrentUser().getUserName());
        warehousingDto.put("remarks",null);
        warehousingDto.put("source",2);
        warehousingDto.put("sourceFrom",null);
        warehousingDto.put("supplierId",orderShip.getSupplierId());
        warehousingDto.put("supplierName",orderShip.getSupplierName());
        warehousingDto.put("totalQuantity", Optional.ofNullable(orderShip.getDtls())
                .map(dtls -> dtls.stream()
                        .map(OrderShipDtl::getShipNum)
                        .filter(Objects::nonNull)  // 过滤掉 null 值
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .orElse(BigDecimal.ZERO));
        warehousingDto.put("warehousingDate",new Date());
        Map<String,Object> warehousingDtlDtos = new HashMap<>();
        orderShip.getDtls().forEach(orderShipDtl -> {
            OrderItem orderItem = orderItemService.getById(orderShipDtl.getOrderItemId());
            if (orderItem.getParentOrderItemId()!=null){
                orderItem=orderItemService.getById(orderItem.getParentOrderItemId());
            }
            OrderSelectPlan plan = orderSelectPlanService.getDataByorderItemId(orderShipDtl.getOrderItemId());
            if (plan != null) {
                warehousingDtlDtos.put("sourceDtlId", plan.getDtlId());
            }
            warehousingDtlDtos.put("billId",orderShipDtl.getBillId());
            warehousingDtlDtos.put("dtlId",orderShipDtl.getDtlId());
            warehousingDtlDtos.put("leaseTime",null);
            warehousingDtlDtos.put("materialClassId",orderShipDtl.getProductCategoryId());
            warehousingDtlDtos.put("materialClassName",orderShipDtl.getProductCategoryName());
            warehousingDtlDtos.put("materialId",orderItem.getProductId());
            warehousingDtlDtos.put("materialName",orderItem.getProductName());
            warehousingDtlDtos.put("placeId",null);
            warehousingDtlDtos.put("receiptQuantity",orderShipDtl.getShipNum());
            warehousingDtlDtos.put("sourceQuantity",null);
            warehousingDtlDtos.put("spec",orderItem.getSkuName());
            warehousingDtlDtos.put("texture",orderItem.getTexture());
            warehousingDtlDtos.put("timeUnit",null);
            warehousingDtlDtos.put("unit",orderItem.getUnit());
            warehousingDtlDtos.put("warehouseId",null);
            warehousingDtlDtos.put("warehouseName",null);
        });
        warehousingDto.put("warehousingDtlDtos",warehousingDtlDtos);
        map.put("warehousingDto",warehousingDto);
        return map;
    }


    private void getDataList(Map<String, Object> request, String idStr, OrderShip orderShip) {
        String url = mallConfig.prodPcwp2Url02 + PCWP2ApiUtil.SAVE_SITE_RECEIVING;
        String content = JSON.toJSONString(request);
        log.info("收料推送：" + content);
        R<Map<String, Object>> r = null;
        String planNo  =null;
        LogUtil.writeInfoLog(idStr, "getDataList", orderShip, request, null, OutSupplierRecevieServiceImpl.class);
        try {
            r = restTemplateUtils.postPCWP2(url, request);
        } catch (Exception e) {
            LogUtil.writeErrorLog(idStr, "getDataList", orderShip, request, null, e.getMessage(), OutSupplierRecevieServiceImpl.class);
            throw new BusinessException("【远程接口异常】" + e.getMessage());

        }
        if (r.getCode() != 200 && r != null) {

            if (r.getMessage().contains("Read timed out")){
                //获取超时计划的计划编号
                planNo = pcwp1Servise.pcwp1VerifyOrderShip(mallConfig.prodPcwp2Url02, idStr);
                if (org.springframework.util.StringUtils.isEmpty(planNo)){
                    LogUtil.writeErrorLog(idStr, "getDataList", orderShip, request, r, r.getMessage(), OutSupplierRecevieServiceImpl.class);
                    log.error("【远程接口异常】收料推送返回报错：收料单推送失败" + r.getMessage());
                    throw new BusinessException("【远程接口异常】" +  r);
                }
            }else {
                LogUtil.writeErrorLog(idStr, "getDataList", orderShip, request, r, r.getMessage(), OutSupplierRecevieServiceImpl.class);
                log.error("【远程接口异常】收料推送返回报错：收料单推送失败" + r.getMessage());
                throw new BusinessException("【远程接口异常】" +  r.getMessage());
            }

        }
        InterfaceLogs iLog = new InterfaceLogs();
        iLog.setSecretKey(idStr);
        iLog.setClassPackage(OutSupplierRecevieServiceImpl.class.getName());
        iLog.setMethodName("getDataList");
        iLog.setLocalArguments(JSON.toJSONString(orderShip));
        iLog.setFarArguments(JSON.toJSONString(content));
        iLog.setIsSuccess(1);
        iLog.setLogType(1);
        iLog.setResult(JSON.toJSONString(r));
        interfaceLogsService.create(iLog);


    }


    private Map<String, Object> getOrderShip(OrderShip orderShip) {
        Map<String, Object> data = new HashMap<>();
        OrderSelectPlan plan = orderSelectPlanService.getDataByOrderSn(orderShip.getOrderSn());
        Orders order = ordersService.getById(orderShip.getOrderId());
        data.put("businessType", orderShip.getSourceType());
        switch (orderShip.getSourceType()) {
            case 1:
                data.put("sourceId", plan.getContractId());
                data.put("sourceNo", plan.getContractNo());
                //合同  不使用完结计划时，约定 type=3 由收料扣减数量
                if (mallConfig.selectMaterialMonthSupplyPlan==0){
                    data.put("type",3);
                }
                break;
            case 2:
                data.put("sourceId", plan.getBillId());
                data.put("sourceNo", plan.getBillNo());
                break;
            case 6:
                data.put("sourceId", plan.getBillId());
                data.put("sourceNo", plan.getBillNo());
                data.put("type", order.getBillType());
                break;
            case 7:
                data.put("sourceId", plan.getBillId());
                data.put("sourceNo", plan.getBillNo());
                break;
            default:
                throw new BusinessException("原单类型错误");
        }


        data.put("storageId", plan.getStorageId());
        data.put("storageName", plan.getStorageName());

        if (StringUtils.isNotBlank(plan.getShortCode())) {
            data.put("orgShort",plan.getShortCode());
        } else {
            data.put("creditCode",  plan.getCreditCode());
        }
        ArrayList<String> userList = new ArrayList<>();
        userList.add(order.getFounderId());
        User byId = userService.getById(order.getFounderId());
        data.put("purchaseId", byId.getInteriorId());
        data.put("purchaseName", order.getFounderName());
        data.put("recorderId", byId.getInteriorId());


        UserLogin user = ThreadLocalUtil.getCurrentUser();
        data.put("receiverId", user.getFarUserId());
        data.put("receiverName", user.getUserName());
        data.put("purchaseUnitId", plan.getOrgId());
        data.put("purchaseUnitName", plan.getOrgName());


        List<OrderShipDtl> list = orderShipDtlService.getDataByOrderShipId(orderShip.getBillId());
        if (list != null && list.size() > 0) {
            //收料单项数据（）
            List<Map<String, Object>> details = getOrderShipDtl(list, orderShip.getSourceType());
            data.put("details", details);
        } else {
            throw new BusinessException("发货单项不能为空");
        }
        EnterpriseInfo enterpriseInfo = enterpriseInfoService.getById(user.getEnterpriseId());
        data.put("orgId", enterpriseInfo.getInteriorId());
        data.put("orgName", enterpriseInfo.getEnterpriseName());
        return data;
    }


    private List<Map<String, Object>> getOrderShipDtl(List<OrderShipDtl> list, Integer sourceType) {
        List<Map<String, Object>> details = new ArrayList<>();
        for (OrderShipDtl dtl : list) {
            Map<String, Object> detail = new HashMap<>();
            detail.put("number", dtl.getShipNum());
            detail.put("invoiceId", dtl.getDtlId());
            Orders orders = ordersService.getById(dtl.getOrderId());
            if (StringUtils.isNotBlank(orders.getParentOrderId())) {
                Orders parent = ordersService.getById(orders.getParentOrderId());
                detail.put("orderId", parent.getOrderId());
                detail.put("orderNo", parent.getOrderSn());
            } else {
                detail.put("orderId", orders.getOrderId());
                detail.put("orderNo", orders.getOrderSn());
            }
            OrderItem orderItem = orderItemService.getById(dtl.getOrderItemId());
            if (orderItem.getParentOrderItemId()!=null){
                orderItem=orderItemService.getById(orderItem.getParentOrderItemId());
            }
            detail.put("tradeId", orderItem.getProductId());
            switch (sourceType) {
                case 1:
                    detail.put("orderId", dtl.getOrderId());
                    detail.put("orderNo", orderItem.getOrderSn());
                    detail.put("texture", orderItem.getTexture());
                    detail.put("spec", orderItem.getSkuName());
                    detail.put("matterUnit", orderItem.getUnit());
                    detail.put("matterId", orderItem.getProductId());
                    detail.put("matterName", orderItem.getProductName());
                    detail.put("materialClassId", orderItem.getClassPathId());
                    detail.put("materialClassName", orderItem.getClassPathName());
                    detail.put("orderDtlId", orderItem.getOrderItemId());
                    OrderSelectPlan plan = orderSelectPlanService.getDataByorderItemId(dtl.getOrderItemId());
                    if (plan != null) {
                        detail.put("sourceDtlId", plan.getDtlId());
                    }
                    break;
                case 2:
                    detail.put("texture", dtl.getTexture());
                    BigDecimal noRatePrice = TaxCalculator.calculateNotTarRateAmount(dtl.getProductPrice(), dtl.getTaxRate());

                    BigDecimal amount = TaxCalculator.noTarRateItemAmount(dtl.getTotalAmount(),noRatePrice,dtl.getShipNum(), dtl.getTaxRate());

                    detail.put("amount", amount);  //总金额
                    detail.put("price", noRatePrice);
                    detail.put("orderDtlId", orderItem.getOrderItemId());
                    //根据订单项封装收料单的物资和分类数据
                    setPlanDetail( detail, orderItem);
                    break;
                case 6:
                    //  //根据订单项封装收料单的物资和分类数据
                    setPlanDetail( detail, orderItem);
                    detail.put("orderDtlId", orderItem.getOrderItemId());
                    detail.put("texture", orderItem.getTexture());
                    BigDecimal price = new BigDecimal(0);
                    if (orders.getBillType()==1){
                        BigDecimal networkPrice = TaxCalculator.calculateNotTarRateAmount(orderItem.getNetPrice(), dtl.getTaxRate());
                        BigDecimal freight = TaxCalculator.calculateNotTarRateAmount(orderItem.getFixationPrice(), dtl.getTaxRate());
                        detail.put("networkPrice", networkPrice.setScale(2,BigDecimal.ROUND_HALF_UP));
                        detail.put("fixedFee", freight.setScale(2,BigDecimal.ROUND_HALF_UP));
                        price=networkPrice.add(freight);

                    }else if (orders.getBillType()==2){
                        BigDecimal factoryPrice = TaxCalculator.calculateNotTarRateAmount(orderItem.getOutFactoryPrice(), dtl.getTaxRate());
                        BigDecimal fixedFee = TaxCalculator.calculateNotTarRateAmount(orderItem.getTransportPrice(), dtl.getTaxRate());
                        detail.put("factoryPrice", factoryPrice.setScale(2,BigDecimal.ROUND_HALF_UP));
                        detail.put("freight",fixedFee.setScale(2,BigDecimal.ROUND_HALF_UP));
                        price=factoryPrice.add(fixedFee);
                    }
                    detail.put("price", price.setScale(2,BigDecimal.ROUND_HALF_UP));
                    detail.put("amount", price.multiply(dtl.getShipNum()).setScale(2,BigDecimal.ROUND_HALF_UP));  //总金额
                    break;
                default:
                    throw new BusinessException("原单类型错误");
            }
            detail.put("spec", dtl.getSkuName());
            detail.put("matterUnit", dtl.getUnit());
            details.add(detail);
        }
        return details;
    }

    private void setPlanDetail( Map<String, Object> detail, OrderItem orderItem) {
//        detail.put("amount", dtl.getTotalAmount().setScale(2,BigDecimal.ROUND_HALF_UP));  //总金额
//        detail.put("price", dtl.getProductPrice().setScale(2,BigDecimal.ROUND_HALF_UP));

//        detail.put("price", dtl.getNoRatePrice());
        //商品的物资数据迁移到订单明细，不用查询商品明细表
//        if (StringUtils.isNotBlank(dtl.getProductId())) {
//            Product product = productService.getByIdLocal(dtl.getProductId());
//            if (product != null) {
        String orderItemId = orderItem.getOrderItemId();
        if (StringUtils.isNotBlank(orderItem.getParentOrderItemId())) {
            OrderItem byId = orderItemService.getById(orderItem.getParentOrderItemId());
            orderItemId = byId.getOrderItemId();

        }
        OrderSelectPlan plan = orderSelectPlanService.getDataByorderItemId(orderItemId);
        if (plan != null) {
            detail.put("sourceDtlId", plan.getDtlId());
        }
        detail.put("matterId", orderItem.getRelevanceId());
        detail.put("matterName", orderItem.getRelevanceName());

        detail.put("materialClassId", orderItem.getClassPathId());
        detail.put("materialClassName", orderItem.getClassPathName());
//            }
//        } else {
//            throw new BusinessException(500, "商品不存在");
//        }
    }
}
