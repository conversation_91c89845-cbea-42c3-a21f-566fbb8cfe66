package scrbg.meplat.mall.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;

import scrbg.meplat.mall.dto.ReturnProductUpdateOrderDTO;
import scrbg.meplat.mall.dto.bidding.CreateBidingByOrderDTO;
import scrbg.meplat.mall.dto.contact.SubmitContactDTO;
import scrbg.meplat.mall.dto.order.BatchCreateTwoOrderDTO;
import scrbg.meplat.mall.dto.order.BatchShipmentsDTO;
import scrbg.meplat.mall.dto.order.CreateMaterialOrderByCartIdsDTO;
import scrbg.meplat.mall.dto.order.CreateRepairOrderDTO;
import scrbg.meplat.mall.dto.order.MasterAffirmTwoOrderDTO;
import scrbg.meplat.mall.dto.order.MergeOrderDTO;
import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.dto.order.UpdateTwoOrderPriceDTO;
import scrbg.meplat.mall.dto.plan.SubmitMonthPlanOrderDTO;
import scrbg.meplat.mall.entity.BiddingPurchase;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.OrderSelectPlan;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.ProductSku;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.SynthesizeTemporary;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailItemVO;
import scrbg.meplat.mall.vo.GetSynthesizeTemporaryPlanDetailVO;
import scrbg.meplat.mall.vo.bidding.HitBidVo;
import scrbg.meplat.mall.vo.platform.PlatformShopCountVo;
import scrbg.meplat.mall.vo.platform.ShopCountVo;
import scrbg.meplat.mall.vo.product.order.OrderCreateResultVO;
import scrbg.meplat.mall.vo.product.restsServe.SettleRepairVO;
import scrbg.meplat.mall.vo.shopManage.reportForms.ProductFromVo;
import scrbg.meplat.mall.vo.user.userCenter.OrderDetailVO;
import scrbg.meplat.mall.vo.user.userCenter.OrderPlanDetail;
import scrbg.meplat.mall.vo.user.userCenter.SettleAccountProductCartVO;
import scrbg.meplat.mall.vo.user.userCenter.SettleAccountProductVO;

/**
 * @描述：商品订单 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
public interface OrdersService extends IService<Orders> {



    /**
     * 根据参数分页查询订单信息列表（平台）
     * @param jsonObject
     * @param queryWrapper
     * @return
     */
    PageUtils listPlatformOrdersByParameters(JSONObject jsonObject, LambdaQueryWrapper<Orders> queryWrapper);

    void create(Orders orders);

    void update(Orders orders);

    Orders getById(String id);

    void delete(String id);


    /**
     * 新增物资订单
     *
     * @param createMaterialOrderDTO
     * @return
     */
    OrderCreateResultVO createMaterialOrder(CreateMaterialOrderByCartIdsDTO createMaterialOrderDTO);

    /**
     * 根据参数查询个人订单信息分页列表
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listUserCenterOrdersByParameters(JSONObject jsonObject, LambdaQueryWrapper<Orders> lambdaQuery);

    /**
     * 合并订单
     * @param dto
     */
    void mergeOrder(MergeOrderDTO dto);

    /**
     * 批量逻辑删除
     * @param ids
     */
    void removeLoginByIds(List<String> ids);

    /**
     * 结算商品
     *
     * @param productId
     * @param buyNum
     * @param leaseNum
     * @return
     */
    SettleAccountProductVO settleAccountProduct(String productId, String buyNum, BigDecimal leaseNum);

    /**
     * 结算购物车
     * @param cartIds
     * @return
     */
    List<SettleAccountProductCartVO> settleAccountProductCart(List<String> cartIds);

    /**
     * 查询我的订单
     * @param jsonObject
     * @return
     */
    PageUtils getUserOrderPageList(JSONObject jsonObject);

    /**
     * 查询我的订单评价
     * @param jsonObject
     * @return
     */
    PageUtils getUserOrderComment(JSONObject jsonObject);

    /**
     * 根据订单编号查询订单信息
     * @param orderSn
     * @return
     */
    Orders findOrderByorderSn(String orderSn);

    /**
     * 获取订单详情
     * @param orderSn
     * @return
     */
    OrderDetailVO getOrderDetail(String orderSn);

    /**
     * 新增装备订单
     * @param dto
     * @return
     */
    Object createDeviceOrder(CreateMaterialOrderByCartIdsDTO dto);

    /**
     * 获取操作计划订单详情
     * @param orderSn
     * @return
     */
    List<OrderPlanDetail> getOrderPlanDetail(String orderSn);

    /**
     * 根据参数分页查询订单信息（店铺）
     * @param jsonObject
     * @param wrapper
     * @return
     */
    PageUtils<Orders> listShopManageOrdersByParameters(JSONObject jsonObject, LambdaQueryWrapper<Orders> wrapper);

    /**
     * 查询我的物流订单
     * @param jsonObject
     * @return
     */
    PageUtils getUserLogisticsOrder(JSONObject jsonObject);


    /**
     * 根据参数分页查询订单信息（平台订单统计）
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils getPlatformOrdersCount(JSONObject jsonObject, LambdaQueryWrapper<Orders> lambdaQuery);

    /**
     * 结算其他服务
     * @param productId
     * @return
     */
    SettleRepairVO settleRepair(String productId);

    /**
     * 新增维修服务订单
     * @param dto
     */
    void createRepairOrder(CreateRepairOrderDTO dto);

    /**
     * 批量修改
     * @param orders
     */
    void updateBatch(List<Orders> orders);

    PlatformShopCountVo getPlatformShopOrderCount(JSONObject jsonObject, QueryWrapper<ShopCountVo> shopCountVoQueryWrapper);

    /**
     * 提交合同
     * @param dto
     */
    void submitContactAndOrder(List<SubmitContactDTO> dto);

    /**
     * 批量发货
     * @param dtos
     */
    void batchShipments(List<BatchShipmentsDTO> dtos);

    /**
     * 个人中心确认收货
     * @param orderId
     */
    void confirmReceipt(String orderId);


    /**
     * 计划详情生成订单
     *
     * @param
     * @param account
     * @param map
     */
    void createMaterialOrder(List<ProductBuyInfoDTO> dtos, String shopId, BigDecimal account, HashMap<Object, Object> map);

    /**
     * 提交计划
     *
     * @param dtos
     * @return
     */
    List<Map> submitPlanAndOrder(List<ProductBuyInfoDTO> dtos);

    /**
     * 采购员App
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils purchaseList(JSONObject jsonObject, LambdaQueryWrapper<Orders> lambdaQuery);

    /**
     * 清除供应商
     * @param orderId
     */
    void closeOrderSupplier(String orderId);

    /**
     * 查询拆的订单
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils getTwoOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> lambdaQuery);

    /**
     * 店铺订单增加供应商
     * @param orders
     */
    void addOrderSupplier(Orders orders);

    /**
     * 确认订单
     * @param orderId
     * @param isAffirm
     */
    void affirmTwoOrder(String orderId, Boolean isAffirm);

    /**
     * 主订单方确认订单
     * @param dto
     */
    void masterAffirmTwoOrder(MasterAffirmTwoOrderDTO dto);

    /**
     * 批量修改二级订单供方价格
     * @param dtos
     */
    void batchUpdateTwoOrderPrice(List<UpdateTwoOrderPriceDTO> dtos);


    /**
     * 关闭订单  舍弃
     * @param orderId
     */
    void closeOrder(String orderId);



    /**
     * 关闭订单  新方法
     * @param orderId
     */
    void closeOrder2(String orderId);

    /**
     * 退货修改金额
     */
    void returnProductUpdateOrder(ReturnProductUpdateOrderDTO dto);



    /**
     * 生成大宗订单
     * @param dto
     */
    void submitMonthPlanOrder(SubmitMonthPlanOrderDTO dto);

    /**
     * 获得订单的所有子订单
     * @param parentOrderId
     * @return
     */
    List<Orders> getTwoOrderByOrderId(String parentOrderId);

    /**
     * 根据主订单项id生成子订单
     * @param dto
     */
    void batchCreateTwoOrder(BatchCreateTwoOrderDTO dto);

    /**
     * 根据订单明细id修改为待竞价状态
     * @param orderItemIds
     */
    void updateToBidingState(List<String> orderItemIds);

    /**
     * 根据订单生成竞价
     * @param dto
     */
    void createBidingByOrder(CreateBidingByOrderDTO dto);

    /**
     * 根据订单明细id修改为未分配状态
     * @param orderItemIds
     */
    void updateNotUseState(List<String> orderItemIds);

    /***
     * 根据订单编号查询订单信息
     * @param orderSn
     * @return
     */
    Orders getDataByOrderSn(String orderSn);


    /**
     * 履约平台采购以员查看订单数据
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */

    PageUtils selectOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> lambdaQuery);


    /**
     * 提交计划(PCWP2 反写计划)
     *
     * @param dtos
     * @return
     */
    List<Map> PCWP2submitPlanAndOrder(List<ProductBuyInfoDTO> dtos);


    /**
     * 计划详情生成订单  PCWP2
     *
     * @param
     * @param
     * @param map
     * idStr  日志id
     * stringBuilder  远程参数
     */
    void PCWP2createMaterialOrder(List<ProductBuyInfoDTO> ProductBuyInfoDTO, String shopId, BigDecimal totalAccount, HashMap<Object, Object> map, String idStr,String stringBuilder);


    /**
     * 根据父级i查询所有子订单
     * @param orderId
     * @return
     */
    List<Orders> getDataByParentId(String orderId);
    /**
     * 回滚反写订单零星采购订单数
     *
     * @param
     * @param
     * @param
     */
    void rollBackPlanAndOrder(String idStr);

    /**
     * 据参数分页查询订单信息（店铺导出）
     * @param jsonObject
     * @param lambdaQuery
     * @param response
     */
    void shopManageListByParametersExport(JSONObject jsonObject, LambdaQueryWrapper<Orders> lambdaQuery, HttpServletResponse response);

    /**
     * 根据订单id导出订单pdf（店铺导出pdf）
     * @param orderId
     * @param response
     */
    void getOrderInfoOutPutPdf(String orderId, HttpServletResponse response);

    /**
     * 据主订单项id生成大宗子订单（选择供方生成二级订单）
     * @param dto
     */
    void batchCreateContractTwoOrder(BatchCreateTwoOrderDTO dto);

    /**
     * 根据分页参数查询大宗合同二级订单
     * @param jsonObject
     * @param lambdaQuery
     * @return
     */
    PageUtils listContractTwoOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> lambdaQuery);

    /**
     * 生成大宗临购订单
     *
     * @param map
     * @param idStr
     * @param farArg
     */
    void submitSynthesizeTemporaryOrder(GetSynthesizeTemporaryPlanDetailVO map, String idStr, StringBuilder farArg);

    /**
     * 生成大宗临购订单
     * @param map
     * @param idStr
     * @param farArg
     */
    void submitInventoryOrder(GetSynthesizeTemporaryPlanDetailVO map, String idStr, StringBuilder farArg);
    // 通过竞价生成订单

    ///**
    // *
    // * @param map 提交的订单信息（明细为竞价数据）
    // * @param hitBidVos 该清单对应的所有数据
    // */
    //void batchCreateBidingOrder(GetSynthesizeTemporaryPlanDetailVO map,List<HitBidVo> hitBidVos);

    /**
     *
     * @param order 一级订单
     * @param synthesizeTemporary 临购清单
     * @param enterpriseInfo 物资公司
     * @param shopOne 自营店
     * @param productSkus 扣减库存
     * @param orderSelectPlans 关联计划
     * @param retPlanList 反写PCWP计划的数据
     * @param outProductId 库存为0的需要下架商品
     * @param orderItems 需要进行拆弹的明细
     * @param details 提交的明细（没竞价的）
     * @param rDto 提交的对象
     * @param taxRate 税率（可为物资公司的，也可为竞价提交的)
     */
    public void batchCreateNotBidingOrder(Orders order,
                                          SynthesizeTemporary synthesizeTemporary,
                                          EnterpriseInfo enterpriseInfo,
                                          Shop shopOne,
                                          ArrayList<ProductSku> productSkus,
                                          ArrayList<OrderSelectPlan> orderSelectPlans,
                                          ArrayList<Map<String, Object>> retPlanList,
                                          List<String> outProductId,
                                          ArrayList<OrderItem> orderItems,
                                       List<GetSynthesizeTemporaryPlanDetailItemVO> details,
                                          GetSynthesizeTemporaryPlanDetailVO rDto,
                                          BigDecimal taxRate
    );
    /**
     *
     * @param order 一级订单
     * @param synthesizeTemporary 临购清单
     * @param enterpriseInfo 物资公司
     * @param shopOne 自营店
     * @param productSkus 扣减库存
     * @param orderSelectPlans 关联计划
     * @param retPlanList 反写PCWP计划的数据
     * @param outProductId 库存为0的需要下架商品
     * @param orderItems 需要进行拆弹的明细
     * @param details 提交的明细(竞价的)
     * @param rDto 提交的对象
     * @param taxRate 税率（可为物资公司的，也可为竞价提交的)
     * @param hitBidVos 竞价明细
     */
    public void batchCreateBidingOrder(Orders order,
                                          SynthesizeTemporary synthesizeTemporary,
                                          EnterpriseInfo enterpriseInfo,
                                          Shop shopOne,
                                          ArrayList<ProductSku> productSkus,
                                          ArrayList<OrderSelectPlan> orderSelectPlans,
                                          ArrayList<Map<String, Object>> retPlanList,
                                          List<String> outProductId,
                                          ArrayList<OrderItem> orderItems,
                                          List<GetSynthesizeTemporaryPlanDetailItemVO> details,
                                          GetSynthesizeTemporaryPlanDetailVO rDto,
                                          BigDecimal taxRate,
                                       List<HitBidVo> hitBidVos
    );
    /**
     * 生成大宗临二级采订单
     *

     */
    void batchCreateLinXingTwoOrder(BatchCreateTwoOrderDTO dto);
    //回调反写临购订单



//    Orders getDataByParentIdAndItemId(String orderId, String itemId);




    /**
     * 删除大宗月供订单
     *

     */
    void deleteYGOrderByOrderId(String orderId);

    /**
     * 删除大宗临购
     *

     */
    void deleteLGOrderByOrderId(String orderId, String keyId, StringBuilder stringBuilder);

    /**
     * 订单总量：订单总量减商城退货总量
     * 发货/收货总量：如果没有收货数量：发货量减去PCWP退货量（如果有收货数量：收货数量减去PCWP退货量）
     * 取最大值
     * @param orderItemIds
     * @return
     */
    BigDecimal getOrderUseCount(List<String> orderItemIds);


    /**
     * 根据订单关联计划明细id获取对应的最大下单的数量
     * @param selectPlanDtlIds
     * @return
     */
    BigDecimal getOrderUseCountBySelectPlanDtlIds(List<String> selectPlanDtlIds);

    /**
     * 根据订单关联计划明细id获取对应的最大下单的数量
     * @param selectPlanDtlId
     * @return
     */
    BigDecimal getOrderUseCountBySelectPlanDtlIds(String selectPlanDtlId);


    /**
     * 修改竞价
     * @param dto
     */
    void updateBidingByOrder(BiddingPurchase dto);

    /**
     * 根据可对账物资获取订单含税单价
     *
     * @param maps
     * @return
     */
    List<Map> getOrderItemPriceByMas(List<Map> maps);

    /**
     * 查询可以选择的竞价采购订单列表
     *
     * @param jsonObject
     * @param ordersLambdaQueryWrapper
     * @return
     */
    PageUtils listBidingOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> ordersLambdaQueryWrapper);


    /**
     * 订单关单
     *
     * @param orderId
     * @param idStr
     * @param farArg
     * @return
     */
    Integer orderCloseClick(String orderId, String idStr, StringBuilder farArg);

    void shopManageListByParametersExport2(JSONObject jsonObject, LambdaQueryWrapper<Orders> ordersLambdaQueryWrapper, HttpServletResponse response);

    void listByParametersLCExport(JSONObject jsonObject, LambdaQueryWrapper<Orders> ordersLambdaQueryWrapper, HttpServletResponse response);

    /**
     * 物资公司根据二级供应商查询可对账的发货单和退货单物资
     * @param jsonObject
     * @param ordersLambdaQueryWrapper
     * @return
     */
    PageUtils getTwoMaterialOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> ordersLambdaQueryWrapper);

    /**
     * 物资公司根据二级供应商查询有可对账物资的发货单和退货单物资
     * @param jsonObject
     * @param ordersLambdaQueryWrapper
     * @return
     */
    PageUtils getHaveTwoMaterialOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> ordersLambdaQueryWrapper);

    PageUtils getTwoSupplierMaterialOrderList(JSONObject jsonObject, LambdaQueryWrapper<Orders> ordersLambdaQueryWrapper);

    void getPlatformOrdersShopCountExcel(JSONObject jsonObject, QueryWrapper<ProductFromVo> productFromVoQueryWrapper, HttpServletResponse response);





    /**
     * 接单
     * @param orderId 订单id
     * @param assigneeId 被派单人id
     */
    void acceptOrder(String orderId, String assigneeId);

    /**
     * 退单
     * @param orderId 订单id
     * @param reason 退单原因
     */
    void rejectOrder(String orderId, String reason);
    
    /**
     * 二级订单导出
     * @param ids
     * @param response
     */
    void supplierExportOrders(List<String> ids, HttpServletResponse response);

    void getPlatformOrdersCountExcel(JSONObject jsonObject, HttpServletResponse response);

    /**
     * 一键复购
     * @param jsonObject
     */
    void oneClickRepurchase(JSONObject jsonObject);


}
