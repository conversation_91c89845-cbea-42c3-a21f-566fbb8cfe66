package scrbg.meplat.mall.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import scrbg.meplat.mall.entity.MaterialReconciliation;
import scrbg.meplat.mall.entity.OrderShip;
import scrbg.meplat.mall.entity.OrderShipDtl;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.vo.ship.*;
import scrbg.meplat.mall.vo.app.ShipFiles;
import scrbg.meplat.mall.vo.shopManage.ReturnGoodsVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @描述： 服务类
 * @作者: ye
 * @日期: 2023-05-22
 */
public interface OrderShipService extends IService<OrderShip> {
        PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> queryWrapper);

        void create(OrderShip orderShip);

        void update(OrderShip orderShip);

        OrderShip getById(String id);

        void delete(String id);

    /**
     * 根据订单项生成发货单
     * @param orderItemList
     */
    void saveOrderShip(OrderShipVo orderItemList);


    boolean confirmShip(SubmitOrderShipVo billId, String idStr, StringBuilder stringBuilder);



    PageUtils selectShipList(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> orderShipLambdaQueryWrapper);

    void updateShipType(OrderShip orderShip);

    /**
     * 供应商根据远程发货单id查询发货单
     * @param outBillId
     * @return
     */
    OrderShip getDataByOutBillId(String outBillId);

     List<OrderShip> getListByOrderId(String orderId);

    /**
     * 更具订单id和发货单类型获取发货单集合
     * @param orderId
     * @return
     */
    List<OrderShip> getListByOrderIdAndType(String orderId);

    void returnGoods(ReturnGoodsVo returnGoodsVo);
   //修改发货单的附件信息
    void updateFiles(ShipFiles shipFiles);

    void exportDataByBillId(String billId, HttpServletResponse response);

    void exportDataTwoById(String id, HttpServletResponse response);

    /**
     * 零星采购发货单查看
     * @param jsonObject
     * @param orderShipLambdaQueryWrapper
     * @return
     */
    PageUtils queryPageTwo(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> orderShipLambdaQueryWrapper);

    void changShipNum(List<SubmitOrderShipDtl> vos);

    void exportDataPurchase(String id, HttpServletResponse response);


    /**
     * 新增退货
     * @param dto
     */
    void createPCWPOrderRetuen(ReturnGoodsVo dto);
    void createPCWPOrderRetuen2(ReturnGoodsVo dto);


    /**
     * 回滚pcwp退货-新增
     * @param keyId
     */
    void rollBackReconciliationCreate(String keyId);

    void pushOrderShipData();

    void wxExportDataPurchase(String id, HttpServletResponse response);

    void materialShipExport(String id, HttpServletResponse response);

    void materialShipDzExport(String id, HttpServletResponse response);

    PageUtils WXqueryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderShip> orderShipLambdaQueryWrapper);

    /**
     * 二级订单生成发货单
     * @param orderShipVo
     */
    void saveTwoOrderShip(OrderShipVo orderShipVo);

    /**
     * 修改发货单数量
     * @param dtls
     */
    void updateDtls(OrderShip vo);


    /**
     * 外部接口
     * 修改大宗月供发货单
     * @param vo
     */
    void updateMonOrderShip(OrderShip vo);

    /**
     * 外部接口
     * 修改大宗零购发货单
     * @param vo
     */
    void UpdateDzOrderShip(OrderShip vo);

    /**
     * 外部接口
     * 供应商发货
     *
     */
    void shippingOrderShip(OrderShip one, User user);

    /**
     * 外部接口
     * 删除大宗月供发货单
     *
     */
    void delMonthOrderShipByBillSn(OrderShip one);
    /**
     * 外部接口
     * 删除大宗临购发货单
     *
     */
    void delDzOrderShipByBillSn(OrderShip one);

    /**
     * 外部接口
     * 删除零星发货单
     *
     */
    void delLiXinBillId(OrderShip one);

    /**
     * 外部接口  供应商生成发货单
     * @param orderShipVo
     */
    void OutSaveOrderShip(OrderShipVo orderShipVo, User user);

    void remindAudit(String enterpriseId);

    /**
     * 定时任务：获取可对帐的物资
     */
    void getMaterialReconciliation();
}
