package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.bcel.generic.IF_ACMPEQ;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.config.redis.redisson.NotResubmit;
import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.entity.plan.PlanDetail;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.OrderReturnMapper;
import scrbg.meplat.mall.mapper.UserMapper;
import scrbg.meplat.mall.pcwp.KeyedPayload;
import scrbg.meplat.mall.pcwp.PcwpRes;
import scrbg.meplat.mall.pcwp.PcwpService;
import scrbg.meplat.mall.pcwp.third.model.TemporaryDemandPlanDtlParams;
import scrbg.meplat.mall.pcwp.third.model.UpdatePlanDtl;
import scrbg.meplat.mall.service.*;
import scrbg.meplat.mall.service.plan.PlanDetailService;
import scrbg.meplat.mall.service.plan.PlanService;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.user.userCenter.OrderReturnVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @描述： 服务类
 * @作者: sund
 * @日期: 2023-01-31
 */
@Service
public class OrderReturnServiceImpl extends ServiceImpl<OrderReturnMapper, OrderReturn> implements OrderReturnService {
    @Autowired
    MallConfig mallConfig;
    @Autowired
    OrderItemService orderItemService;
    @Autowired
    ShopService shopService;
    @Autowired
    OrderReturnItemService orderReturnItemService;
    @Autowired
    OrdersService ordersService;
    @Autowired
    BrandService brandService;
    @Autowired
    ProductService productService;
    @Autowired
    ProductSkuService productSkuService;
    @Autowired
    EnterpriseInfoService enterpriseInfoService;

    @Autowired
    OrderSelectPlanService orderSelectPlanService;

    @Autowired
    private PlanService planService;

    @Autowired
    UserMapper userMapper;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<OrderReturn> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        Integer businessType = (Integer) innerMap.get("businessType");
        String startDate = (String) innerMap.get("startDate");
        String abovePrice = (String) innerMap.get("abovePrice");
        Integer sourceType = (Integer) innerMap.get("sourceType");
        String belowPrice = (String) innerMap.get("belowPrice");
        String endDate = (String) innerMap.get("endDate");
        Integer state = (Integer) innerMap.get("state");
        queryWrapper.eq(state != null, OrderReturn::getState, state);
        if (!StringUtils.isEmpty(keywords)) {
            queryWrapper.like(OrderReturn::getOrderSn, keywords).or()
                    .like(OrderReturn::getOrderReturnNo, keywords).or()
                    .like(OrderReturn::getUntitled, keywords);

        }
        if (!StringUtils.isEmpty(sourceType)) {
            queryWrapper.eq(OrderReturn::getSourceType, sourceType);
        }
//        if (!StringUtils.isEmpty(businessType)) {
//            queryWrapper.eq(OrderReturn::getSourceType, businessType);
//        }
        if (!StringUtils.isEmpty(startDate)) {
            queryWrapper.gt(OrderReturn::getGmtCreate, startDate);
        }
        if (!StringUtils.isEmpty(endDate)) {
            queryWrapper.lt(OrderReturn::getGmtCreate, endDate);
        }
        if (!StringUtils.isEmpty(abovePrice)) {
            queryWrapper.gt(OrderReturn::getTotalAmount, abovePrice);
        }
        if (!StringUtils.isEmpty(belowPrice)) {
            queryWrapper.lt(OrderReturn::getTotalAmount, belowPrice);
        }

        queryWrapper.eq(OrderReturn::getMallType, mallConfig.mallType);
        queryWrapper.orderByDesc(OrderReturn::getGmtCreate);
        IPage<OrderReturn> page = this.page(
                new Query<OrderReturn>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(OrderReturn orderReturn) {
        List<OrderReturnItem> itemList = orderReturn.getOrderReturnItems();
        Orders orders = ordersService.getById(orderReturn.getOrderId());
        Integer state = orders.getState();
        if (itemList != null && itemList.size() > 0) {
            //商城退货的id为主订单id
            if (orders.getOrderClass() != 2) {
                if (!StringUtils.isEmpty(orders.getOrderClass())) {
                    switch (orders.getOrderClass()) {
                        case 1:
                            orderReturn.setOrderClass(1);
                            saveOrderReturnByorderInfo(orders, orderReturn);
                            break;
                        case 3:
                            orderReturn.setOrderClass(3);
                            sonsaveOrderReturnByorderInfo(orders, orderReturn);
                            break;
                        default:
                            break;
                    }
                }
//                saveItems(orderReturn, itemList);
            } else {
                //二级订单  获取所有主订单项id
                List<String> orderItemIds = itemList.stream().map(s -> s.getOrderItemId()).collect(Collectors.toList());
                //根据订单项集合查询所有子订单项的信息
                List<OrderItem> list = orderItemService.selectsonAllByIds(orderItemIds);
                //根据供应商id区分不同的二级订单
                Map<String, List<OrderItem>> collect = list.stream().collect(Collectors.groupingBy(s -> s.getSupplierId()));
                collect.forEach((supplierId, ordersItemList) -> {

                    //由于二级订单是由不同的
                    OrderReturn info = new OrderReturn();
                    BeanUtils.copyProperties(orderReturn, info);
                    info.setOrderClass(2);

                    saveOrderReturnByorderInfo(orders, info);
                    //根据子订单项确认子订单信息
                    OrderItem itemInfo = ordersItemList.get(0);
                    Orders son = ordersService.getById(itemInfo.getOrderId());
                    //添加二级订单信息，如果双方为确认，则订单算是自营店
                    saveOrderReturnOther(son, info);

                    StringBuilder untitled = new StringBuilder();
                    //计算金额
                    BigDecimal otherNoRateAmount = BigDecimal.valueOf(0);
                    BigDecimal otherTotalAmount = BigDecimal.valueOf(0);
                    BigDecimal noRateAmount = BigDecimal.valueOf(0);
                    BigDecimal totalAmount = BigDecimal.valueOf(0);
                    BigDecimal othertaxRate = BigDecimal.valueOf(0);
                    BigDecimal taxRate = BigDecimal.valueOf(0);
                    //orderReturnItems  来自于参数 ，获取退货数量
                    List<OrderReturnItem> orderReturnItems = info.getOrderReturnItems();

                    for (OrderReturnItem orderReturnItem : orderReturnItems) {
                        //ordersItemList  获取二级订单项数据
                        for (OrderItem sonOrderItem : ordersItemList) {

                            //根据 订单项找出相对应的自订单数据
                            if (sonOrderItem.getParentOrderItemId().equals(orderReturnItem.getOrderItemId())) {
                                if (orderReturnItem != null) {
                                    OrderItem orderItem = orderItemService.getById(orderReturnItem.getOrderItemId());
                                    untitled = untitled.append(orderItem.getProductName() + ',');
                                    orderReturnItem.setOrderReturnNo(info.getOrderReturnNo());
                                    orderReturnItem.setOrderReturnId(info.getOrderReturnId());

                                    orderReturnItem.setOrderId(info.getOrderId());
                                    orderReturnItem.setOrderSn(info.getOrderSn());


                                    orderReturnItem.setProductSn(orderItem.getProductSn());
                                    orderReturnItem.setProductId(orderItem.getProductId());
                                    orderReturnItem.setProductName(orderItem.getProductName());
                                    orderReturnItem.setProductImg(orderItem.getProductImg());
                                    orderReturnItem.setBrandName(orderItem.getBrandName());
                                    orderReturnItem.setBrandId(orderItem.getBrandId());
                                    orderReturnItem.setSkuName(orderItem.getSkuName());
                                    orderReturnItem.setBuyCounts(orderItem.getBuyCounts());
                                    orderReturnItem.setOrderItemId(orderItem.getOrderItemId());
                                    orderReturnItem.setProductPrice(orderItem.getProductPrice());

                                    //修改退货数量
                                    BigDecimal count = orderReturnItem.getCount();
                                    //旧退货数量

                                    BigDecimal remanentCount = orderReturnItemService.getDataByOrderItmId(orderItem.getOrderItemId(), 0);
                                    //旧发货数量
                                    BigDecimal shipCounts = orderItem.getShipCounts();
                                    //得到不可退货数量
                                    remanentCount = shipCounts.add(remanentCount).add(count);

                                    //比较退货数量
                                    if (orderItem.getBuyCounts().compareTo(remanentCount) > 0) {
                                        orderReturnItem.setTotalAmount(orderReturnItem.getCount().multiply(orderItem.getProductPrice()));
                                        //计算含税和不含税价格累加
                                        BigDecimal notRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getTotalAmount(), orderReturnItem.getNoRatePrice(), orderReturnItem.getCount(), taxRate);
                                        totalAmount = totalAmount.add(orderReturnItem.getTotalAmount());
                                        noRateAmount = noRateAmount.add(notRateAmount);
                                        if (info.getOrderClass() != 2) {
                                            orderReturnItem.setOtherOrderSn(info.getOtherOrderSn());
                                            orderReturnItem.setOtherOrderId(info.getOtherOrderId());
                                            orderReturnItem.setOtherOrderItemId(sonOrderItem.getOrderItemId());
                                            orderReturnItem.setOtherProductPrice(sonOrderItem.getProductPrice());

                                            orderReturnItem.setOtherRateAmount(orderReturnItem.getCount().multiply(orderReturnItem.getOtherProductPrice()));
                                            BigDecimal noOtherTarRateAmount = TaxCalculator.noTarRateItemAmount(orderReturnItem.getOtherRateAmount(), orderReturnItem.getOtherNoProductPrice(), orderReturnItem.getCount(), othertaxRate);
                                            orderReturnItem.setOtherNoRateAmount(noOtherTarRateAmount);
                                            otherNoRateAmount = otherNoRateAmount.add(noOtherTarRateAmount);
                                            otherTotalAmount = otherTotalAmount.add(orderReturnItem.getOtherRateAmount());
                                        }

                                        sonOrderItem.setReturnCounts(orderItem.getReturnCounts());

                                        //修改订单项退货数量
                                        orderItem.setReturnCounts(orderItem.getReturnCounts().add(orderReturnItem.getCount()));
                                        ProductSku sku = productSkuService.getById(orderItem.getSkuId());
                                        BigDecimal newStock = sku.getStock().add(orderReturnItem.getCount());
                                        sku.setStock(newStock);
                                        //修改库存
                                        productSkuService.update(sku);
                                        orderItemService.update(orderItem);
                                        orderItemService.update(sonOrderItem);
                                        orderReturnItemService.save(orderReturnItem);

                                    } else {
                                        throw new BusinessException(500, orderItem.getProductName() + "退货数量超出可退货数量，退货失败");
                                    }
                                } else {
                                    throw new BusinessException(500, "退货数量不能大于剩余数量（购买数量-已退货数量-发货中的数量）");
                                }

                                updateOrderReturn(orderReturn, untitled, otherNoRateAmount, otherTotalAmount, noRateAmount, totalAmount);

                            }
                        }
                    }
//
                });

            }


        } else {
            throw new BusinessException(500, "退货项,请选择退货项");
        }


//
    }

    private void updateOrderReturn(OrderReturn orderReturn, StringBuilder untitled, BigDecimal otherNoRateAmount, BigDecimal otherTotalAmount, BigDecimal noRateAmount, BigDecimal totalAmount) {
        orderReturn.setUntitled(untitled.toString());
        orderReturn.setOtherRateAmount(otherTotalAmount);
        orderReturn.setOtherNoRateAmount(otherNoRateAmount);

        orderReturn.setRateAmount(totalAmount);
        orderReturn.setTotalAmount(totalAmount);
        orderReturn.setNoRateAmount(noRateAmount);

        updateById(orderReturn);
    }

    //如果是二级订单
    private void sonsaveOrderReturnByorderInfo(Orders orders, OrderReturn orderReturn) {
        //主数据
        Orders parentOrder = ordersService.getById(orders.getParentOrderId());
        saveOrderReturnByorderInfo(parentOrder, orderReturn);
        //其他数据
        saveOrderReturnOther(orders, orderReturn);

    }

    private void saveOrderReturnOther(Orders sonOrders, OrderReturn orderReturn) {
        //如果自营店和二级供应商
        if (sonOrders.getAffirmState() == 1) {
            orderReturn.setOrderClass(3);
            orderReturn.setOtherOrderSn(sonOrders.getOrderSn());
            orderReturn.setOtherOrderId(sonOrders.getOrderId());
            orderReturn.setShipEnterpriseId(sonOrders.getSupplierId());
            orderReturn.setShipEnterpriseName(sonOrders.getSupplierName());
        }
    }

    //如果是普通订单
    private void saveOrderReturnByorderInfo(Orders orders, OrderReturn orderReturn) {

        String orderReturnNo = UUID.randomUUID().toString().replace("-", "");
        orderReturn.setOrderReturnNo(orderReturnNo);
        orderReturn.setOrderSn(orders.getOrderSn());
        save(orderReturn);
    }
//订单先退货


    @Override
    public void addDate(OrderReturnVo orderReturnVo) {

    }

    @Override
    public void update(OrderReturn orderReturn) {
        updateById(orderReturn);

    }


    @Override
    public OrderReturn getById(String id) {
        OrderReturn byId = super.getById(id);
        List<OrderReturnItem> items = orderReturnItemService.selectAllByOrderReturmId(byId.getOrderReturnId());
        byId.setOrderReturnItems(items);
        return byId;
    }

    @Override
    public OrderReturn getByIdAndTwo(String id) {
//        OrderReturn orderReturn = super.getById(id);
//        Orders orders = ordersService.getById(orderReturn.getOtherOrderItemId());
//        orderReturn.setShopName(orders.getShopName());
//        return orderReturn;
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(String id) {
        OrderReturn byId = getById(id);
        super.removeById(id);
        ordersService.closeOrder2(byId.getOrderId());
    }

    @Override
    public PageUtils getUserOrderReturnPageList(@NotNull JSONObject jsonObject, QueryWrapper<OrderReturn> wrapper) {

        Map<String, Object> innerMap = jsonObject.getInnerMap();
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        String enterpriseId = user.getEnterpriseId();
        String keywords = (String) innerMap.get("keywords");
        String startDate = (String) innerMap.get("startDate");
        String endDate = (String) innerMap.get("endDate");
        Integer state = (Integer) innerMap.get("state");
        if (state != null) {
            if (!StringUtils.isEmpty(state)) {
                wrapper.eq("ore.state", state);
            }
        }
        wrapper.eq("ore.enterprise_id", enterpriseId);
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt("ore.gmt_create", startDate);
        }
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt("ore.gmt_create", endDate);
        }
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.like("ore.order_sn", keywords).or()
                    .like("ori.product_name", keywords);

        }
//        wrapper.eq("ore.mall_type", mallConfig.mallType);
//        wrapper.eq("ore.is_delete", 0);
        wrapper.orderByDesc("ore.gmt_create");
        IPage<OrderReturn> pages = new Query<OrderReturn>().getPage(jsonObject);


        List<OrderReturn> list = baseMapper.findByCondition(pages, wrapper);

//        for (String s : orderSnSet) {
//            HashMap<String, Object> hashMap = new HashMap<>();
//            hashMap.put("orderNum", s);
//            ArrayList<Object> products = new ArrayList<>();
//            for (OrderReturnVo orderReturnVo : list) {
//                if (s.equals(orderReturnVo.getOrderSn())) {
//                    products.add(orderReturnVo);
//                }
//                hashMap.put("products", products);
//            }
//            orderList.add(hashMap);
//        }
        PageUtils pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;
    }

    @Override
    public PageUtils shopOrderReturnList(JSONObject jsonObject, LambdaQueryWrapper<OrderReturn> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String startDate = (String) innerMap.get("startDate");
        Integer sourceType = (Integer) innerMap.get("sourceType");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String abovePrice = (String) innerMap.get("abovePrice");
        String belowPrice = (String) innerMap.get("belowPrice");
        String endDate = (String) innerMap.get("endDate");
        Integer state = (Integer) innerMap.get("state");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        wrapper.eq(OrderReturn::getSupplierId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        if (!StringUtils.isEmpty(state)) {
            wrapper.eq(state != null, OrderReturn::getState, state);
        }
        wrapper.eq(sourceType != null, OrderReturn::getSourceType, sourceType);
        wrapper.eq(orderClass != null, OrderReturn::getOrderClass, orderClass);
        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt(OrderReturn::getGmtCreate, startDate);
        }
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt(OrderReturn::getGmtCreate, endDate);
        }
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.like(OrderReturn::getOrderSn, keywords).or().like(OrderReturn::getUntitled, keywords);


        }
        if (!StringUtils.isEmpty(abovePrice)) {
            wrapper.gt(OrderReturn::getTotalAmount, abovePrice);
        }
        if (!StringUtils.isEmpty(belowPrice)) {
            wrapper.lt(OrderReturn::getTotalAmount, belowPrice);
        }
        wrapper.eq(MustBaseEntity::getIsDelete, 0);
        if (orderBy != null) {
            if (orderBy == 2) {
                wrapper.orderByDesc(OrderReturn::getGmtCreate);
            } else {
                wrapper.orderByDesc(OrderReturn::getGmtCreate);
            }
        } else {
            wrapper.orderByDesc(OrderReturn::getGmtCreate);
        }
        IPage<OrderReturn> page = this.page(
                new Query<OrderReturn>().getPage(jsonObject),
                wrapper
        );
        return new PageUtils(page);

    }

    @Autowired
    PcwpService pcwpService;

    //修改退货状态
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateState(OrderReturn orderReturn) {
        OrderReturn info = baseMapper.selectById(orderReturn.getOrderReturnId());
        Orders orders = ordersService.getById(info.getOrderId());
        User user = userMapper.selectById(orders.getUserId());
        Plan plan = planService.getByBillId(orders.getPlanId());
        if (orderReturn.getState() == 3) {
            // 废弃，因为只要申请退货数量就有效
            Integer state = orders.getState();
//            if(state == 10 || state == 9) {
//                throw new BusinessException("该订单已发货完毕或已完成不能退货！");
//            }
//            // 已经发货则校验
//            // 后续打开
            if (state == 8) {
                List<OrderReturnItem> items = orderReturnItemService.getReturnNo(info.getOrderReturnNo());
                // 校验
                for (OrderReturnItem orderReturnItem : items) {
                    String orderItemId = orderReturnItem.getOrderItemId();
                    OrderItem oi = orderItemService.getById(orderItemId);
                    // 当订单收货数量-pwcp退货数量>=订单数量-商城退货数量,并且所有发货单都已收货，则表示订单已完成。
                    BigDecimal mReturnCounts = oi.getReturnCounts();
                    BigDecimal pcwpRetuen = oi.getPcwpReturn();
                    if (oi.getBuyCounts().subtract(mReturnCounts).compareTo(oi.getShipCounts().subtract(pcwpRetuen)) > 0) {
                        // 拿到可退货
                        BigDecimal num1 = oi.getBuyCounts().subtract(mReturnCounts);
                        BigDecimal num2 = oi.getShipCounts().subtract(pcwpRetuen);
                        if (num1.subtract(num2).compareTo(orderReturnItem.getCount()) < 0) {
                            throw new BusinessException("商品：" + orderReturnItem.getProductName() + "退货数量超过剩余可商城退货数量！可商城退货数量：" + num1.subtract(num2));
                        }
                    } else {
                        throw new BusinessException("该订单已全部发货完毕，不可商城退货！");
                    }
                }
            }
        }
        info.setState(orderReturn.getState());
        info.setFlishTime(new Date());
        if (orderReturn.getState() == 4) {
            List<OrderReturnItem> items = orderReturnItemService.getReturnNo(info.getOrderReturnNo());
            for (OrderReturnItem item : items) {
                OrderItem orderItem = orderItemService.getById(item.getOrderItemId());
                orderItem.setReturnState(orderReturn.getState());
                orderItem.setReturnCounts(orderItem.getReturnCounts().subtract(item.getCount()));
                if (orderItem.getParentOrderItemId() != null) {
                    OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
                    parent.setReturnState(orderReturn.getState());
                    parent.setReturnCounts(orderItem.getReturnCounts());
                    orderItemService.update(parent);
                } else {
                    OrderItem son = orderItemService.getOrderItemByParentId(orderItem.getOrderItemId());
                    if (son != null) {
                        son.setReturnState(orderReturn.getState());
                        son.setReturnCounts(orderItem.getReturnCounts());
                        orderItemService.update(son);
                    }
                }

                orderItemService.update(orderItem);

            }
        } else if (orderReturn.getState() == 3) {
            List<OrderReturnItem> items = orderReturnItemService.getReturnNo(info.getOrderReturnNo());
            for (OrderReturnItem item : items) {
                OrderItem orderItem = orderItemService.getById(item.getOrderItemId());
                orderItem.setReturnState(orderReturn.getState());
                if (orderItem.getParentOrderItemId() != null) {
                    OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
                    parent.setReturnState(orderReturn.getState());
                    orderItemService.update(parent);
                } else {
                    OrderItem son = orderItemService.getOrderItemByParentId(orderItem.getOrderItemId());
                    if (son != null) {
                        son.setReturnState(orderReturn.getState());
                        orderItemService.update(son);
                    }
                }
                orderItemService.update(orderItem);
            }
        }
        updateById(info);
        ordersService.closeOrder2(info.getOrderId());
    }

    @Override
    public void updateState2(OrderReturn orderReturn) {

        if (orderReturn == null || orderReturn.getOrderReturnId() == null) {
            throw new BusinessException("退货单信息不完整");
        }

        OrderReturn info = baseMapper.selectById(orderReturn.getOrderReturnId());
        if (info == null) {
            throw new BusinessException("未找到对应的退货单");
        }

        Orders orders = ordersService.getById(info.getOrderId());
        if (orders == null) {
            throw new BusinessException("未找到对应的订单");
        }


        //如果审核通过，state == 3 ;走退货流程
        if (orderReturn.getState() == 3) {
            handleReturnApproval(info, orders);
        }

        //如果不审核通过，state == 4 ;
        if (orderReturn.getState() == 4) {
            handleReturnRejection(info);
        }
        info.setFlishTime(new Date());
        info.setState(orderReturn.getState());
        updateById(info);
        ordersService.closeOrder2(info.getOrderId());

    }

    private void handleReturnRejection(OrderReturn info) {
        List<OrderReturnItem> items = orderReturnItemService.getReturnNo(info.getOrderReturnNo());
        for (OrderReturnItem item : items) {
            updateOrderItemReturnState(item, 4);

            OrderItem orderItem = orderItemService.getById(item.getOrderItemId());
            // 恢复可退货数量
            orderItem.setReturnCounts(orderItem.getReturnCounts().subtract(item.getCount()));
            orderItemService.update(orderItem);
        }
    }

    //仅处理已发货状态的订单
    private void handleReturnApproval(OrderReturn info, Orders orders) {

        List<OrderReturnItem> items = orderReturnItemService.getReturnNo(info.getOrderReturnNo());
        //校验 发货中状态是否可退货
        validateReturnItem(items, orders);
        //校验通过，处理退货
        for (OrderReturnItem item : items) {
            //更新订单状态
            updateOrderItemReturnState(item, 3);
        }

        // 处理库存归还
        handleInventoryReturn(items, orders);
    }

    private void handleInventoryReturn(List<OrderReturnItem> items, Orders orders) {

        List<String> productIds = items.stream().map(OrderReturnItem::getProductId).collect(Collectors.toList());
        User user = userMapper.selectById(orders.getUserId());
        if (user != null && user.getIsInternalUser() == 1) {
            String idStr = IdWorker.getIdStr();
            Plan plan = planService.getByBillId(orders.getPlanId());
            List<PlanDetail> planDetails = planDetailService.lambdaQuery().
                    eq(PlanDetail::getBillId, plan.getBillId())
                    .in(PlanDetail::getTradeId, productIds).list();

            if (!planDetails.isEmpty()) {
                int planType = plan.getType();

                for (PlanDetail planDetail : planDetails) {

                    Optional<OrderReturnItem> opbi = items.stream().filter(d -> d.getProductId().equals(planDetail.getTradeId())).findAny();
                    if (!opbi.isPresent()) {
                        continue;
                    }
                    OrderReturnItem productBuyInfo = opbi.get();
                    Integer consumeNumber = planDetail.getConsumeNumber();
                    Integer notConsumeNumber = planDetail.getNotConsumeNumber();
                    int cartNum = productBuyInfo.getCount().intValue();
                    consumeNumber = Math.max((consumeNumber - cartNum), 0);
                    notConsumeNumber = notConsumeNumber + cartNum;
                    BigDecimal taxPrice = planDetail.getTaxPrice();
                    BigDecimal consumeAmount = taxPrice.multiply(new BigDecimal(consumeNumber));
                    BigDecimal notConsumeAmount = taxPrice.multiply(new BigDecimal(notConsumeNumber));
                    planDetail.setConsumeNumber(consumeNumber);
                    planDetail.setNotConsumeNumber(notConsumeNumber);
                    planDetail.setConsumeAmount(consumeAmount);
                    planDetail.setNotConsumeAmount(notConsumeAmount);
                }
                // 更新本地计划里的 已消耗金额 consumeAmount; 未消耗金额 notConsumeAmount; 已消耗数量 consumeNumber; 未消耗数量 notConsumeNumber;四项的值
                planDetailService.updateBatchById(planDetails);

                List<UpdatePlanDtl> updatePlanDtls = getUpdatePlanDtl(items, plan.getPBillId(), planDetails);

                PcwpRes<Void> res;
                try {
                    // 这里要根据计划类型判断调用哪个接口（零星，大宗，周材）
                    if (planType == 0) {
                        res = pcwpService.updateRetailPlanDtl(KeyedPayload.<List<UpdatePlanDtl>>builder()
                                .data(updatePlanDtls)
                                .keyId(idStr)
                                .isReturn(true)
                                .orgId(ThreadLocalUtil.getCurrentUser().getOrgId())
                                .build());
                    } else if (planType == 1) {
                        res = pcwpService.updateBulkRetailPlanDtl(KeyedPayload.<List<UpdatePlanDtl>>builder()
                                .data(updatePlanDtls)
                                .keyId(idStr)
                                .isReturn(true)
                                .orgId(ThreadLocalUtil.getCurrentUser().getOrgId())
                                .build());
                    } else if (planType == 2) {
                        res = pcwpService.updateRevolPlanDtl(KeyedPayload.<List<UpdatePlanDtl>>builder()
                                .data(updatePlanDtls)
                                .keyId(idStr)
                                .isReturn(true)
                                .orgId(ThreadLocalUtil.getCurrentUser().getOrgId())
                                .build());
                    } else {
                        throw new BusinessException("未知的计划类型" + planType);
                    }
                } catch (Exception e) {
                    log.error("调用反写计划接口异常：", e);
                    throw new BusinessException("远程接口调用异常！" + e.getMessage());
                }
                if (res.getCode() == null || res.getCode() != 200) {
                    log.error("反写计划接口返回错误：" + res.getMessage());
                    throw new BusinessException("远程接口调用失败！" + res.getMessage());
                }
            }

        }

        items.forEach(e -> {
            ProductSku sku = productSkuService.getProductSkuById(e.getSkuId(), 0);
            if (sku != null) {
                // 这里原代码可能有逻辑问题，买的数量减去退货数量？
                // 建议根据实际业务逻辑调整
                BigDecimal newStock = sku.getStock()
                        .add(e.getBuyCounts())
                        .subtract(e.getCount());
                sku.setStock(newStock);
                productSkuService.update(sku);
            }
        });


    }

    private void updateOrderItemReturnState(OrderReturnItem item, Integer state) {
        OrderItem orderItem = orderItemService.getById(item.getOrderItemId());
        if (orderItem == null) {
            throw new BusinessException("未找到对应的订单商品: " + item.getProductName());
        }

        orderItem.setReturnState(state);
        orderItemService.update(orderItem);

        // 更新关联的父/子订单商品
        if (orderItem.getParentOrderItemId() != null) {
            OrderItem parent = orderItemService.getById(orderItem.getParentOrderItemId());
            if (parent != null) {
                parent.setReturnState(state);
                orderItemService.update(parent);
            }
        } else {
            OrderItem son = orderItemService.getOrderItemByParentId(orderItem.getOrderItemId());
            if (son != null) {
                son.setReturnState(state);
                son.setReturnCounts(orderItem.getReturnCounts());
                orderItemService.update(son);
            }
        }
    }

    private List<UpdatePlanDtl> getUpdatePlanDtl(List<OrderReturnItem> items, String pBillId, List<PlanDetail> planDetails) {
        List<UpdatePlanDtl> updatePlanDtls = new ArrayList<>(items.size());
        for (OrderReturnItem dto : items) {
            PlanDetail planDetail = planDetails.stream().filter(p -> p.getTradeId().equals(dto.getProductId())).findAny().get();
            UpdatePlanDtl updatePlanDtl = UpdatePlanDtl.builder().amount(dto.getNoRatePrice())
                    .number(dto.getCount())
                    .dtlId(pBillId)
                    .billId(planDetail.getPDtlId())
                    .build();
            updatePlanDtls.add(updatePlanDtl);
        }
        return updatePlanDtls;
    }

    private void validateReturnItem(List<OrderReturnItem> items, Orders orders) {
        if (orders.getState() == 8) {

            if (items.isEmpty()) {
                throw new BusinessException("退货单没有关联商品");
            }

            for (OrderReturnItem item : items) {
                OrderItem orderItem = orderItemService.getById(item.getOrderItemId());
                if (orderItem == null) {
                    throw new BusinessException("未找到对应的订单商品: " + item.getProductName());
                }

                BigDecimal mReturnCounts = orderItem.getReturnCounts();
                BigDecimal pcwpReturn = orderItem.getPcwpReturn();
                BigDecimal buyCounts = orderItem.getBuyCounts();
                BigDecimal shipCounts = orderItem.getShipCounts();

                // 计算可退货数量
                BigDecimal availableReturn = buyCounts.subtract(mReturnCounts)
                        .subtract(shipCounts.subtract(pcwpReturn));

                if (availableReturn.compareTo(BigDecimal.ZERO) <= 0) {
                    throw new BusinessException("该订单已全部发货完毕，不可商城退货！");
                }

                if (item.getCount().compareTo(availableReturn) > 0) {
                    throw new BusinessException(String.format(
                            "商品：%s 退货数量超过剩余可商城退货数量！可退货数量：%s",
                            item.getProductName(), availableReturn));
                }
            }
        }
    }

    //修改退货状态
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTwoOrderItemState(OrderReturn orderReturn) {
        OrderReturn info = baseMapper.selectById(orderReturn.getOrderReturnId());


        info.setState(orderReturn.getState());
        update(info);
        List<OrderReturnItem> items = orderReturnItemService.getReturnNo(orderReturn.getOrderReturnNo());
        for (OrderReturnItem item : items) {
            OrderItem orderItem = orderItemService.getById(item.getOrderItemId());
            if (orderReturn.getState() != 4) {
                //退货审核失败，订单退货数量  -1；
                orderItem.setReturnCounts(orderItem.getReturnCounts().subtract(item.getCount()));
                if (item.getOtherOrderItemId() != null) {
                    OrderItem son = orderItemService.getById(item.getOtherOrderItemId());
                    son.setReturnCounts(son.getReturnCounts().add(item.getCount()));
                    orderItem.setReturnState(orderReturn.getState());
                    orderItemService.update(son);
                }
            } else {
                if (item.getOtherOrderItemId() != null) {
                    OrderItem son = orderItemService.getById(item.getOtherOrderItemId());
                    orderItem.setReturnState(orderReturn.getState());
                    orderItemService.update(son);
                }

            }
            orderItem.setReturnState(orderReturn.getState());
            orderItemService.update(orderItem);
        }

    }


//    @Override
//    public PageUtils getReturnOrderList(JSONObject jsonObject, QueryWrapper<OrderReturnVo> orderReturnVoQueryWrapper) {
//        Map<String, Object> innerMap = jsonObject.getInnerMap();
//        String keywords = (String) innerMap.get("keywords");
//        String startDate = (String) innerMap.get("startDate");
//        String endDate = (String) innerMap.get("endDate");
//        Integer state = (Integer) innerMap.get("state");
//        return null;
//    }


    @Override
    public BigDecimal getOrderItem(String orderItemId) {
        QueryWrapper<OrderReturn> wp = new QueryWrapper<>();
        wp.select("sum(count)")
                .eq("order_item_id", orderItemId)
                .eq("mall_type", mallConfig.mallType)
                .eq("state", 2);
        Map<String, Object> map = this.getMap(wp);

        //退款成功后设置
//        wp.eq(OrderReturn::getState,4);


        return null;
    }


    @Override
    public BigDecimal getRetuenSumByCount(String orderItemId) {
        QueryWrapper<OrderReturn> wp = new QueryWrapper<>();
        wp.select("IFNULL(sum(count),0) as sum")
//                .eq("order_item_id", orderItemId)
                .eq("mall_type", mallConfig.mallType)
                //TODO  展示
                .ne("state", 4);
        Map<String, Object> map = this.getMap(wp);
        Double sum = Double.parseDouble(map.get("sum").toString());
        BigDecimal bigDecimal = new BigDecimal(sum);
        return bigDecimal;

    }


    @Override
    public PageUtils getOrderItemListByOrderReturnId(String orderReturnId) {
        OrderReturn orderReturn = getById(orderReturnId);
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
//        wrapper.eq(OrderItem::getOrderItemId, orderReturn.getOrderItemId());
        List<OrderItem> list = orderItemService.list(wrapper);
        PageUtils<OrderItem> pageUtils = new PageUtils<>();
        pageUtils.setList(list);
        return pageUtils;
    }

    @Override
    public PageUtils getOrderItemTwoListByOrderReturnId(String orderReturnId) {
        OrderReturn orderReturn = getById(orderReturnId);
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        List<OrderItem> list = orderItemService.list(wrapper);
        PageUtils<OrderItem> pageUtils = new PageUtils<>();
        pageUtils.setList(list);
        return pageUtils;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageUtils twoListByEntity(JSONObject jsonObject, LambdaQueryWrapper<OrderReturn> wrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String startDate = (String) innerMap.get("startDate");
        String abovePrice = (String) innerMap.get("abovePrice");
        Integer isOut = (Integer) innerMap.get("isOut");
        Integer sourceType = (Integer) innerMap.get("sourceType");
        Integer orderClass = (Integer) innerMap.get("orderClass");
        String belowPrice = (String) innerMap.get("belowPrice");
        String endDate = (String) innerMap.get("endDate");
        Integer state = (Integer) innerMap.get("state");
        Integer orderBy = (Integer) innerMap.get("orderBy");
        wrapper.eq(OrderReturn::getShipEnterpriseId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        wrapper.eq(isOut != null, OrderReturn::getIsOut, isOut);
        wrapper.eq(orderClass != null, OrderReturn::getOrderClass, orderClass);

        wrapper.eq(OrderReturn::getSourceType, sourceType);
        if (!StringUtils.isEmpty(state)) {
            wrapper.eq(OrderReturn::getState, state);
        }

        if (!StringUtils.isEmpty(startDate)) {
            wrapper.gt(OrderReturn::getGmtCreate, startDate);
        }
        if (!StringUtils.isEmpty(endDate)) {
            wrapper.lt(OrderReturn::getGmtCreate, endDate);
        }
        if (!StringUtils.isEmpty(keywords)) {
            wrapper.like(OrderReturn::getOrderSn, keywords).or().like(OrderReturn::getUntitled, keywords);


        }
        if (!StringUtils.isEmpty(abovePrice)) {
            wrapper.gt(OrderReturn::getTotalAmount, abovePrice);
        }
        if (!StringUtils.isEmpty(belowPrice)) {
            wrapper.lt(OrderReturn::getTotalAmount, belowPrice);
        }
        wrapper.eq(MustBaseEntity::getIsDelete, 0);
        if (orderBy != null) {
            if (orderBy == 2) {
                wrapper.orderByDesc(OrderReturn::getGmtCreate);
            } else {
                wrapper.orderByDesc(OrderReturn::getGmtCreate);
            }
        } else {
            wrapper.orderByDesc(OrderReturn::getGmtCreate);
        }
        IPage<OrderReturn> page = this.page(
                new Query<OrderReturn>().getPage(jsonObject),
                wrapper
        );
        return new PageUtils(page);
    }

    @Autowired
    PlanDetailService planDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @NotResubmit
    public void createText(OrderReturn orderReturn) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        List<OrderReturnItem> itemList = orderReturn.getOrderReturnItems();
        orderReturn.setUserId(user.getUserId());
        orderReturn.setEnterpriseId(user.getEnterpriseId());
        orderReturn.setEnterpriseName(user.getEnterpriseName());
        orderReturn.setIsOut(0);
        orderReturn.setState(1);
        Orders orders = ordersService.getById(orderReturn.getOrderId());
        Integer state = orders.getState();
        if (state == 10 || state == 9) {
            throw new BusinessException("该订单已发货完毕或已完成不能退货！");
        }
        // 已经发货则校验
        if (state == 8) {
            // 校验
            for (OrderReturnItem orderReturnItem : itemList) {
                String orderItemId = orderReturnItem.getOrderItemId();
                OrderItem oi = orderItemService.getById(orderItemId);
                // 当订单收货数量-pwcp退货数量>=订单数量-商城退货数量,并且所有发货单都已收货，则表示订单已完成。
                BigDecimal mReturnCounts = oi.getReturnCounts();
                BigDecimal pcwpRetuen = oi.getPcwpReturn();
                //  订单数量-商城退货>发货数量-pcwp退货数量时，为未全部发货
                if (oi.getBuyCounts().subtract(mReturnCounts).compareTo(oi.getShipCounts().subtract(pcwpRetuen)) > 0) {
                    // 拿到可退货
                    BigDecimal num1 = oi.getBuyCounts().subtract(mReturnCounts);
                    BigDecimal num2 = oi.getShipCounts().subtract(pcwpRetuen);
                    if (num1.subtract(num2).compareTo(orderReturnItem.getCount()) < 0) {
                        throw new BusinessException("商品：" + orderReturnItem.getProductName() + "退货数量超过剩余可商城退货数量！可商城退货数量：" + num1.subtract(num2));
                    }
                } else {
                    throw new BusinessException("该订单已全部发货完毕，不可商城退货！");
                }
            }
        }

        createDataByOrders(orderReturn, orders);
        for (OrderReturnItem orderReturnItem : itemList) {
            if (orderReturnItem.getCount() == null || orderReturnItem.getCount().compareTo(BigDecimal.valueOf(0)) == 0) {
                throw new BusinessException(500, "退货数量不能为空或者0");
            }
        }
        if (orders.getOrderClass() == 2) {
            for (OrderReturnItem orderReturnItem : itemList) {
                OrderItem byId = orderItemService.getById(orderReturnItem.getOrderItemId());
                //以前商城退货数量
                BigDecimal localCount = orderReturnItemService.getDataByOrderItmId(orderReturnItem.getOrderItemId(), 0);
                BigDecimal sum = localCount.add(byId.getShipCounts().subtract(byId.getPcwpReturn())).add(orderReturnItem.getCount());
                //可退货数量=商城推货数量+发货数量-pcwp退货数量+本次退货数量
                if (byId.getBuyCounts().compareTo(sum) >= 0) {
                    orderReturnItemService.setOrderReturn(byId, orderReturnItem, orders.getTaxRate());
                    OrderItem sonOrdersItem = orderItemService.getOrderItemByParentId(byId.getOrderItemId());
                    if (sonOrdersItem != null) {
                        Orders sonOrders = ordersService.getById(sonOrdersItem.getOrderId());
                        if (sonOrders.getAffirmState() == 1) {
                            orderReturnItemService.setOrderReturn(sonOrdersItem, orderReturnItem, sonOrders.getTaxRate());
                            //修改退货数量

                        } else {
                            orderReturnItem.setOtherOrderId(byId.getOrderId());
                            orderReturnItem.setOtherOrderSn(byId.getOrderSn());
                        }
                        sonOrdersItem.setState(1);
                        sonOrdersItem.setReturnCounts(byId.getReturnCounts().add(orderReturnItem.getCount()));
                        orderItemService.update(sonOrdersItem);

                    } else {
                        orderReturnItem.setOtherOrderId(byId.getOrderId());
                        orderReturnItem.setOtherOrderSn(byId.getOrderSn());
                    }
                    byId.setReturnState(1);
                    byId.setReturnCounts(byId.getReturnCounts().add(orderReturnItem.getCount()));
                    orderItemService.update(byId);
                } else {
                    throw new BusinessException(500, "退货数量已经超过可退货数量，退货失败");
                }

            }
            Map<String, List<OrderReturnItem>> collect = itemList.stream().collect(Collectors.groupingBy(s -> s.getOtherOrderId()));
            collect.forEach((orderId, ordersItemList) -> {
                OrderReturn info = new OrderReturn();
                BeanUtils.copyProperties(orderReturn, info);
                String orderReturnNo = UUID.randomUUID().toString().replace("-", "");
                info.setOrderReturnNo(orderReturnNo);

                Orders son = ordersService.getById(orderId);
                if (son.getAffirmState() == 1) {
                    info.setOrderClass(2);
                } else {
                    info.setOrderClass(1);
                }
                info.setOtherOrderId(orderId);
                info.setOtherOrderSn(son.getOrderSn());
                info.setShipEnterpriseId(son.getSupplierId());
                info.setShipEnterpriseName(son.getEnterpriseName());

                save(info);
                StringBuilder untitled = new StringBuilder();
                BigDecimal otherNoRateAmount = BigDecimal.valueOf(0);
                BigDecimal otherTotalAmount = BigDecimal.valueOf(0);
                BigDecimal noRateAmount = BigDecimal.valueOf(0);
                BigDecimal totalAmount = BigDecimal.valueOf(0);
                for (OrderReturnItem orderReturnItem : ordersItemList) {
                    untitled.append(orderReturnItem.getProductName());
                    orderReturnItem.setIsOut(0);


                    orderReturnItem.setOrderReturnId(info.getOrderReturnId());
                    orderReturnItem.setOrderReturnNo(info.getOrderReturnNo());
                    if (orders.getProductType() != 12) {
                        if (orders.getOrderId().equals(orderId)) {
                            info.setOrderClass(1);
                            noRateAmount = noRateAmount.add(orderReturnItem.getNoRateAmount());
                            totalAmount = totalAmount.add(orderReturnItem.getTotalAmount());
                        } else {
                            noRateAmount = noRateAmount.add(orderReturnItem.getNoRateAmount());
                            totalAmount = totalAmount.add(orderReturnItem.getTotalAmount());
                            otherTotalAmount = otherTotalAmount.add(orderReturnItem.getOtherRateAmount());
                            otherNoRateAmount = otherNoRateAmount.add(orderReturnItem.getOtherNoRateAmount());
                        }
                    }

                }
                orderReturnItemService.saveBatch(ordersItemList);
                info.setTotalAmount(totalAmount);
                info.setNoRateAmount(noRateAmount);
                info.setOtherRateAmount(otherTotalAmount);
                info.setOtherNoRateAmount(otherNoRateAmount);
                info.setUntitled(untitled.toString());
                info.setState(1);
                update(info);
            });
        } else if (orders.getOrderClass() == 1) {
            String orderReturnNo = UUID.randomUUID().toString().replace("-", "");
            orderReturn.setOrderReturnNo(orderReturnNo);
            orderReturn.setOrderSn(orders.getOrderSn());
            orderReturn.setOrderClass(1);
            save(orderReturn);
            StringBuilder untitled = new StringBuilder();
            BigDecimal noRateAmount = BigDecimal.valueOf(0);
            BigDecimal totalAmount = BigDecimal.valueOf(0);
            for (OrderReturnItem orderReturnItem : orderReturn.getOrderReturnItems()) {
                OrderItem byId = orderItemService.getById(orderReturnItem.getOrderItemId());
                BigDecimal localCount = orderReturnItemService.getDataByOrderItmId(orderReturnItem.getOrderItemId(), 0);
                BigDecimal sum = localCount.add(byId.getShipCounts().subtract(byId.getPcwpReturn())).add(orderReturnItem.getCount());
                if (byId.getBuyCounts().compareTo(sum) >= 0) {
                    byId.setReturnState(1);
                } else {
                    throw new BusinessException(500, "退货数量已经超过可退货数量，退货失败");
                }

                untitled.append(byId.getProductName());
                orderReturnItem.setIsOut(0);
                orderReturnItem.setOrderReturnId(orderReturn.getOrderReturnId());
                orderReturnItem.setOrderReturnNo(orderReturn.getOrderReturnNo());
                orderReturnItem.setBuyCounts(byId.getBuyCounts());
                orderReturnItem.setProductName(byId.getProductName());
                orderReturnItem.setSkuName(byId.getSkuName());
                orderReturnItem.setUnit(byId.getUnit());
                orderReturnItem.setTexture(byId.getTexture());
                orderReturnItem.setProductImg(byId.getProductImg());
                if (orders.getProductType() != 12) {
                    BigDecimal count = orderReturnItem.getCount();
                    BigDecimal productPrice = byId.getProductPrice();
                    BigDecimal productNoPrice = TaxCalculator.calculateNotTarRateAmount(productPrice, orders.getTaxRate());
                    BigDecimal itemAmount = count.multiply(productPrice).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal itemNoAmount = TaxCalculator.noTarRateItemAmount(itemAmount, productNoPrice, count, orders.getTaxRate());
                    orderReturnItem.setProductPrice(productPrice);
                    orderReturnItem.setTotalAmount(itemAmount);
                    orderReturnItem.setNoRateAmount(itemNoAmount);
//                   noRateAmount=noRateAmount.add(orderReturnItem.getNoRateAmount());
//                   totalAmount=totalAmount.add(orderReturnItem.getTotalAmount());
                    totalAmount = totalAmount.add(itemAmount);
                    noRateAmount = noRateAmount.add(itemNoAmount);

                }
                byId.setReturnState(1);
                byId.setReturnCounts(byId.getReturnCounts().add(orderReturnItem.getCount()));
                orderItemService.update(byId);

            }
            orderReturn.setTotalAmount(totalAmount);
            orderReturn.setNoRateAmount(noRateAmount);
            orderReturn.setUntitled(untitled.toString());
            orderReturnItemService.saveBatch(orderReturn.getOrderReturnItems());
            update(orderReturn);
        }
    }


    //
//    @Override
//    public void deleteByLikeNo(String keyId) {
//        LambdaQueryWrapper<OrderReturn> q = new LambdaQueryWrapper<>();
//        q.eq(OrderReturn::getOrderReturnNo,keyId);
//
//
//    }


    @Override
    public void createDataByOrders(OrderReturn orderReturn, Orders orders) {
        orderReturn.setOrderId(orders.getOrderId());
        orderReturn.setOrderSn(orders.getOrderSn());
        orderReturn.setShopId(orders.getShopId());
        orderReturn.setShopName(orders.getShopName());
        orderReturn.setSupplierId(orders.getSupplierId());
        orderReturn.setSupplierName(orders.getSupplierName());
//        if (orders.getProductType() == 0) {
//            orderReturn.setSourceType(2);
//        } else if (orders.getProductType() == 12) {
//            orderReturn.setSourceType(1);
//        } else if (orders.getProductType() == 2) {
//            orderReturn.setSourceType(8);
//        } else if (orders.getProductType() == 14 && (orders.getOrderClass() == 1 || orders.getOrderClass() == 3)) {
//            orderReturn.setSourceType(7);
//        } else if (orders.getProductType() == 13) {
//            orderReturn.setSourceType(6);
//            orderReturn.setBillType(orders.getBillType());
//            orderReturn.setBillType(orders.getBillType());
//        }

        if (orders.getProductType() == 0) {
            orderReturn.setSourceType(2);
        } else if (orders.getProductType() == 1) {
            orderReturn.setSourceType(6);
            orderReturn.setBillType(orders.getBillType());
            orderReturn.setBillType(orders.getBillType());
        } else if (orders.getProductType() == 2) {
            orderReturn.setSourceType(7);
        } else if (orders.getProductType() == 14 && (orders.getOrderClass() == 1 || orders.getOrderClass() == 3)) {
            orderReturn.setSourceType(7);
        } else if (orders.getProductType() == 13) {
            orderReturn.setSourceType(6);
            orderReturn.setBillType(orders.getBillType());
            orderReturn.setBillType(orders.getBillType());
        }

    }
}
