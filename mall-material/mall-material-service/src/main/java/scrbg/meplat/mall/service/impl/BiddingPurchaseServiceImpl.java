package scrbg.meplat.mall.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import scrbg.meplat.mall.common.constant.ProcessConstants;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.dto.bidding.*;
import scrbg.meplat.mall.dto.order.BatchCreateTwoOrderDTO;
import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.enums.product.OrderEnum;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.BiddingPurchaseMapper;
import scrbg.meplat.mall.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.*;
import scrbg.meplat.mall.vo.bidding.*;
import scrbg.meplat.mall.vo.product.StationMessageReceiveVO;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.BatchUpdateException;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @描述：竞价采购表 服务类
 * @作者: ye
 * @日期: 2023-07-11
 */
@Service
public class BiddingPurchaseServiceImpl extends ServiceImpl<BiddingPurchaseMapper, BiddingPurchase> implements BiddingPurchaseService {


    @Autowired
    private BiddingProductService biddingProductService;

    @Autowired
    private AuditRecordService auditRecordService;

    @Autowired
    private OrdersService ordersService;

    @Autowired
    private OrderItemService orderItemService;

    @Autowired
    private BiddingBidRecordService biddingBidRecordService;

    @Autowired
    private BiddingBidRecordItemService recordItemService;

    @Autowired
    private BiddingSuppliersService biddingSuppliersService;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    private FileService fileService;

    @Autowired
    private BiddingWinRecordService biddingWinRecordService;

    @Autowired
    private BiddingBidRecordItemService biddingBidRecordItemService;

    @Autowired
    private ShopSupplierReleService shopSupplierReleService;


    @Autowired
    ShopService shopService;

    @Autowired
    BiddingInvitationRelevanceService biddingInvitationRelevanceService;
    @Autowired
    StationMessageService stationMessageService;
    @Resource
    private SynthesizeTemporaryService synthesizeTemporaryService;
    @Resource
    private SynthesizeTemporaryDtlService synthesizeTemporaryDtlService;

    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> queryWrapper) {
        queryWrapper.eq(BiddingPurchase::getPublicityState, 1);
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        queryWrapper.eq(BiddingPurchase::getShopId, user.getShopId());
        IPage<BiddingPurchase> page = getWarpper(jsonObject, queryWrapper);
        return new PageUtils(page);
    }

    private IPage<BiddingPurchase> getWarpper(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String biddingSn = (String) innerMap.get("biddingSn");
        Integer biddingSourceType = (Integer) innerMap.get("biddingSourceType");
        Integer biddingState = (Integer) innerMap.get("biddingState");
        Integer state = (Integer) innerMap.get("state");
        String orderSn = (String) innerMap.get("orderSn");
        String keywords = (String) innerMap.get("keywords");
        String title = (String) innerMap.get("title");
        String starStartTime = (String) innerMap.get("starStartTime");
        String endStartTime = (String) innerMap.get("endStartTime");
        String endTime = (String) innerMap.get("endTime");

        if (biddingSn != null && biddingSn != "") {
            queryWrapper.eq(BiddingPurchase::getBiddingSn, biddingSn);
        }
        queryWrapper.eq(biddingSourceType != null, BiddingPurchase::getBiddingSourceType, biddingSourceType);
        queryWrapper.eq(biddingState != null, BiddingPurchase::getBiddingState, biddingState);
        queryWrapper.eq(state != null, BiddingPurchase::getState, state);
        if (title != null && title != "") {
            queryWrapper.like(BiddingPurchase::getTitle, title);
        }
        if (starStartTime != null && starStartTime != "") {
            queryWrapper.gt(BiddingPurchase::getStartTime, starStartTime);
        }
        if (endStartTime != null && endStartTime != "") {
            queryWrapper.lt(BiddingPurchase::getStartTime, endStartTime);
        }
        if (endTime != null && endTime != "") {
            queryWrapper.lt(BiddingPurchase::getBiddingSn, endTime);
        }
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(BiddingPurchase::getTitle, keywords)
                        .or()
                        .like(BiddingPurchase::getBiddingSn, keywords);

            });
        }

        IPage<BiddingPurchase> page = this.page(
                new Query<BiddingPurchase>().getPage(jsonObject),
                queryWrapper
        );
        List<BiddingPurchase> records = page.getRecords();
        if (records != null && records.size() > 0) {
            ArrayList<BiddingPurchase> list = new ArrayList<>();
            for (BiddingPurchase record : records) {
//                if (record.getEndTime().compareTo(new Date())<0){
//                    if (record.getBiddingState()!=3){
//                        record.setBiddingState(3);
//                        list.add(record);
//                    }
//                }
            }
            updateBatchById(list);
        }
        return page;
    }

    @Override
    public void create(BiddingPurchase biddingPurchase) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(biddingPurchase);
    }

    @Override
    public void update(BiddingPurchase biddingPurchase) {
        super.updateById(biddingPurchase);
    }


    @Override
    public BiddingPurchase getById(String id) {
        return super.getById(id);
    }

    @Override
    public void bidOpening(String id) {
        BiddingPurchase biddingPurchase = super.getById(id);
        biddingPurchase.setState(4);
        super.updateById(biddingPurchase);

    }

    @Override
    public void deadlineTime(BiddingPurchase biddingPurchase) {
        biddingPurchase.setEndTime(Date.from(Instant.parse(biddingPurchase.getDeadlineTime())));
        super.updateById(biddingPurchase);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    /**
     * 未登录时产看全部竞价信息
     *
     * @param jsonObject
     * @param wrapper
     * @return
     */
    @Override
    public PageUtils unLoginqueryPage(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> wrapper) {
        IPage<BiddingPurchase> page = getWarpper(jsonObject, wrapper);
        return new PageUtils(page);
    }

    /**
     * 查询我发布竞价列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listMyCreateBiding(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> q) {
        q.eq(BiddingPurchase::getCreateOrgId, ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        String keywords = (String) jsonObject.get("keywords");
        String biddingSn = (String) jsonObject.get("biddingSn");
        String title = (String) jsonObject.get("title");
        String startDate = (String) jsonObject.get("startDate");
        String endDate = (String) jsonObject.get("endDate");
        String linkName = (String) jsonObject.get("linkName");
        String linkPhone = (String) jsonObject.get("linkPhone");
        Integer state = (Integer) jsonObject.get("state");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        Integer productType = (Integer) jsonObject.get("productType");
        String createStartDate = (String) jsonObject.get("createStartDate");
        String createEndDate = (String) jsonObject.get("createEndDate");
        Integer biddingState = (Integer) jsonObject.get("biddingState");
        Integer publicityState = (Integer) jsonObject.get("publicityState");
        Integer type = (Integer) jsonObject.get("type");
        if (orderBy != null) {
            if (orderBy == 1) {
                q.orderByDesc(BiddingPurchase::getGmtCreate);
            }
            if (orderBy == 2) {
                q.orderByDesc(BiddingPurchase::getStartTime);
            }
            if (orderBy == 3) {
                q.orderByDesc(BiddingPurchase::getEndTime);
            }
        }
        q.select(BiddingPurchase.class, f -> {
            return !f.getProperty().equals("biddingExplain") && !f.getProperty().equals("remarks");
        });
        q.eq(state != null, BiddingPurchase::getState, state);
        q.eq(biddingState != null, BiddingPurchase::getBiddingState, biddingState);
        q.eq(productType != null, BiddingPurchase::getProductType, productType);
        q.eq(type != null, BiddingPurchase::getType, type);
        q.eq(publicityState != null, BiddingPurchase::getPublicityState, publicityState);
        if (StringUtils.isNotBlank(biddingSn)) q.like(BiddingPurchase::getBiddingSn, biddingSn.trim());
        if (StringUtils.isNotBlank(title)) q.like(BiddingPurchase::getTitle, title.trim());
        if (StringUtils.isNotBlank(linkName)) q.like(BiddingPurchase::getLinkName, linkName.trim());
        if (StringUtils.isNotBlank(linkPhone)) q.like(BiddingPurchase::getLinkPhone, linkPhone.trim());
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), BiddingPurchase::getEndTime, startDate, endDate);
        q.between(StringUtils.isNotEmpty(createStartDate) && StringUtils.isNotEmpty(createEndDate), BiddingPurchase::getGmtCreate, createStartDate, createEndDate);
        if (!org.springframework.util.StringUtils.isEmpty(keywords)) {
            q.and((t) -> {
                t.like(BiddingPurchase::getBiddingSn, keywords)
                        .or()
                        .like(BiddingPurchase::getTitle, keywords)
                        .or()
                        .like(BiddingPurchase::getSynthesizeTemporarySn, keywords);
            });
        }
        q.orderByDesc(BiddingPurchase::getGmtCreate);
        IPage<BiddingPurchase> page = this.page(
                new Query<BiddingPurchase>().getPage(jsonObject),
                q
        );
        List<BiddingPurchase> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageUtils(page);
        } else {
            for (BiddingPurchase record : records) {
                Date endTime = record.getEndTime();
                // 如果截止时间小于当前时间
                if (endTime.compareTo(new Date()) < 0) {
                    // 结束了
                    if (record.getBiddingState() != 3) {
                        lambdaUpdate().eq(BiddingPurchase::getBiddingId, record.getBiddingId())
                                .set(BiddingPurchase::getBiddingState, 3)
                                .set(BiddingPurchase::getGmtModified, new Date()).update();
                        record.setBiddingState(3);
                    }
                } else {
                    // 没有到达结束时间
                    // 判断是否通过并且发布了，修改状态为进行中
                    if (record.getState() == 5) {
                        if (record.getBiddingState() != 2) {
                            lambdaUpdate().eq(BiddingPurchase::getBiddingId, record.getBiddingId())
                                    .set(BiddingPurchase::getBiddingState, 2)
                                    .set(BiddingPurchase::getGmtModified, new Date()).update();
                            record.setBiddingState(2);
                        }
                    }
                }
            }
            return new PageUtils(page);
        }
    }

//    @Override
//    public List<BiddingBidRecord> getBiddingRecordInfo() {
//        List<BiddingBidRecord> biddingBidRecords = biddingBidRecordService.lambdaQuery()
////                .eq(BiddingBidRecord::getBiddingId, biddingPurchase.getBiddingId())
////                .ne(BiddingBidRecord::getState, 0)
//                .orderByDesc(BiddingBidRecord::getGmtCreate)
//                .list();
//        for (BiddingBidRecord re : biddingBidRecords) {
//            File file = fileService.lambdaQuery()
//                    .eq(File::getRelevanceId, re.getBidRecordId())
//                    .eq(File::getRelevanceType, 15).one();
//            if (file != null) {
//                re.setFileFarId(file.getFileFarId());
//                re.setFileName(file.getName());
//            }
//        }
//        return biddingBidRecords;
//    }

    @Autowired
    ProcessConfigService processConfigService;

    @Autowired
    ProcessInstanceService processInstanceService;

    @Autowired
    AuditService auditService;

    /**
     * 获取竞价详细信息
     *
     * @param biddingSn
     * @return
     */
    @Override
    public BiddingPurchaseInfoVO getBiddingPurchaseInfo(String biddingSn) {
        BiddingPurchase biddingPurchase = lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn).one();
        BiddingPurchaseInfoVO vo = new BiddingPurchaseInfoVO();
        if (biddingPurchase == null) {
            return vo;
        }
        // 获取主体信息
        BeanUtils.copyProperties(biddingPurchase, vo);

        List<BiddingProduct> biddingProducts = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingSn, biddingSn).list();
        vo.setBiddingProducts(biddingProducts);

        // 获取审核历史
//        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
//                .eq(AuditRecord::getRelevanceType, 3)
//                .orderByDesc(AuditRecord::getGmtCreate)
//                .eq(AuditRecord::getRelevanceId, biddingPurchase.getBiddingId()).list();
//        List<AuditRecord> auditRecords1 = auditRecords.stream().peek(e -> {
//            e.setNoticeTime(new Date());
//            e.setReviewSecondary("一般审核人");
//        }).collect(Collectors.toList());
//        vo.setAuditRecords(auditRecords1);
        List<AuditRecords> auditRecords = getAuditRecords(biddingPurchase);
        vo.setAuditRecords(auditRecords);
        // 获取报价历史
//        List<BiddingBidRecord> biddingBidRecords = biddingBidRecordService.lambdaQuery()
//                .eq(BiddingBidRecord::getBiddingId, biddingPurchase.getBiddingId())
//                .ne(BiddingBidRecord::getState, 0)
//                .orderByDesc(BiddingBidRecord::getGmtCreate)
//                .list();
//        for (BiddingBidRecord re : biddingBidRecords) {
//            File file = fileService.lambdaQuery()
//                    .eq(File::getRelevanceId, re.getBidRecordId())
//                    .eq(File::getRelevanceType, 15).one();
//            if (file != null) {
//                re.setFileFarId(file.getFileFarId());
//                re.setFileName(file.getName());
//            }
//        }

        List<BidOfferInfoVo> productList = new ArrayList<>();
        List<BiddingBidRecord> bidRecords = biddingBidRecordService.lambdaQuery().eq(BiddingBidRecord::getBiddingId, biddingPurchase.getBiddingId()).list();

        if (bidRecords.size() > 0) {
            for (BiddingBidRecord bidRecord : bidRecords) {
                List<BiddingBidRecordItem> itemList = recordItemService.lambdaQuery()
                        .eq(BiddingBidRecordItem::getBidRecordId, bidRecord.getBidRecordId())
                        .orderByDesc(BiddingBidRecordItem::getGmtModified).list();

                //根据明细表的竞价采购商品ID 查询竞价商品表的商品数据
                for (BiddingBidRecordItem item : itemList) {
                    BidOfferInfoVo info = new BidOfferInfoVo();
                    info.setSupplierName(bidRecord.getSupplierName());
                    // 组装竞价明细
                    info.setBidPrice(item.getBidPrice());
                    info.setState(bidRecord.getState());
                    info.setBidState(0);
                    info.setRemarks(item.getRemarks());
                    info.setTaxRate(item.getTaxRate());
                    info.setBidRatePrice(item.getBidRatePrice());
                    info.setBidRateAmount(item.getBidRateAmount());
                    info.setBidAmount(item.getBidAmount());
                    //info.setRemarks(item.getRemarks());
                    String productId = item.getBiddingProductId();
                    // 组装物资明细
                    BiddingProduct product = biddingProductService.lambdaQuery()
                            .eq(BiddingProduct::getBiddingProductId, productId)
                            //.eq(BiddingProduct::getBiddingSn,biddingSn)
                            .one();
                    info.setDeliveryAddress(product.getDeliveryAddress());
                    Date deliveryDate = product.getDeliveryDate();
                    if (deliveryDate == null) {
                        info.setDeliveryDate(null);
                    } else {
                        info.setDeliveryDate(DateUtil.getyyymmdd(deliveryDate));
                    }
                    info.setUnit(product.getUnit());
                    info.setProductName(product.getProductName());
                    info.setSpec(product.getSpec());
                    info.setBiddingProductId(product.getBiddingProductId());
                    info.setProductTexture(product.getProductTexture());
                    info.setNum(product.getNum());
                    // 限价
                    info.setReferencePrice(product.getReferencePrice());
                    // 大宗商品
                    if (biddingPurchase.getProductType() == 1 || biddingPurchase.getProductType() == 2) {
                        if (biddingPurchase.getBillType() == 1) {
                            // 浮动
                            info.setNetPrice(item.getNetPrice());
                            info.setFixationPrice(item.getFixationPrice());
                        }
                        if (biddingPurchase.getBillType() == 2) {
                            // 固定
                            info.setOutFactoryPrice(item.getOutFactoryPrice());
                            info.setTransportPrice(item.getTransportPrice());
                        }
                    }
                    productList.add(info);
                }
            }

            Map<String, Object> map = new ConcurrentHashMap<>();
            List<BidOfferInfoVo> productListSort = new ArrayList<>();
            Map<String, List<BidOfferInfoVo>> groupByGrade = productList.stream()
                    .collect(Collectors.groupingBy(BidOfferInfoVo::getSupplierName));
            int xh = 0;

            for (String key : groupByGrade.keySet()) {
                ++xh;
                BidOfferInfoVo bidOfferInfoVo = new BidOfferInfoVo();
                List<BidOfferInfoVo> bidOfferInfoVos = groupByGrade.get(key);
                map.put("materialsNum", groupByGrade.get(key).size());
                int finalXh = xh;
                List<BidOfferInfoVo> collect = bidOfferInfoVos.stream().peek(e -> {
                    e.setXh(finalXh);
                }).collect(Collectors.toList());
                double sum = collect.stream()
                        .mapToDouble(e -> e.getBidRateAmount().doubleValue())
                        .sum();
                bidOfferInfoVo.setProductName("小计");
                bidOfferInfoVo.setSpec(sum + "");
                collect.add(bidOfferInfoVo);
                productListSort.addAll(collect);

            }
            map.put("materialsData", productListSort);
            vo.setBiddingBidRecordsMap(map);
        }

        List<BiddingWinRecord> winRecords = biddingWinRecordService.lambdaQuery().eq(BiddingWinRecord::getBiddingId, biddingPurchase.getBiddingId()).list();
        List<BidOfferInfoVo> winningBidResult = new ArrayList();
        if (winRecords.size() > 0) {
            BiddingWinRecord biddingWinRecord = winRecords.get(0);
            BiddingBidRecord biddingBidRecord = biddingBidRecordService.getById(biddingWinRecord.getBidRecordId());

            List<BiddingBidRecordItem> itemList = recordItemService.lambdaQuery()
                    .eq(BiddingBidRecordItem::getBidRecordId, biddingBidRecord.getBidRecordId())
                    .orderByDesc(BiddingBidRecordItem::getGmtModified).list();

            //根据明细表的竞价采购商品ID 查询竞价商品表的商品数据
            for (BiddingBidRecordItem item : itemList) {
                BidOfferInfoVo info = new BidOfferInfoVo();
                info.setSupplierName(biddingBidRecord.getSupplierName());
                // 组装竞价明细
                info.setBidPrice(item.getBidPrice());
                info.setBidState(biddingBidRecord.getState());
                info.setRemarks(item.getRemarks());
                info.setTaxRate(item.getTaxRate());
                info.setBidRatePrice(item.getBidRatePrice());
                info.setBidRateAmount(item.getBidRateAmount());
                info.setBidAmount(item.getBidAmount());
                //info.setRemarks(item.getRemarks());
                String productId = item.getBiddingProductId();
                // 组装物资明细
                BiddingProduct product = biddingProductService.lambdaQuery()
                        .eq(BiddingProduct::getBiddingProductId, productId)
                        //.eq(BiddingProduct::getBiddingSn,biddingSn)
                        .one();
                info.setDeliveryAddress(product.getDeliveryAddress());
                Date deliveryDate = product.getDeliveryDate();
                if (deliveryDate == null) {
                    info.setDeliveryDate(null);
                } else {
                    info.setDeliveryDate(DateUtil.getyyymmdd(deliveryDate));
                }
                info.setUnit(product.getUnit());
                info.setProductName(product.getProductName());
                info.setSpec(product.getSpec());
                info.setBiddingProductId(product.getBiddingProductId());
                info.setProductTexture(product.getProductTexture());
                info.setNum(product.getNum());
                // 限价
                info.setReferencePrice(product.getReferencePrice());
                // 大宗商品
                if (biddingPurchase.getProductType() == 1 || biddingPurchase.getProductType() == 2) {
                    if (biddingPurchase.getBillType() == 1) {
                        // 浮动
                        info.setNetPrice(item.getNetPrice());
                        info.setFixationPrice(item.getFixationPrice());
                    }
                    if (biddingPurchase.getBillType() == 2) {
                        // 固定
                        info.setOutFactoryPrice(item.getOutFactoryPrice());
                        info.setTransportPrice(item.getTransportPrice());
                    }
                }
                winningBidResult.add(info);
            }
        }

        vo.setWinningBidResult(winningBidResult);

        // 供应商列表
        List<BiddingSuppliers> biddingSuppliers = biddingSuppliersService.lambdaQuery()
                .eq(BiddingSuppliers::getBiddingId, biddingPurchase.getBiddingId())
                .orderByDesc(BiddingSuppliers::getGmtCreate)
                .list();
        vo.setBiddingSuppliers(biddingSuppliers);
        if (biddingPurchase.getType() == 2) {
            // 邀请供应商列表
            List<BiddingInvitationRelevance> relevances = biddingInvitationRelevanceService.lambdaQuery().eq(BiddingInvitationRelevance::getBiddingSn, biddingPurchase.getBiddingSn()).list();
            vo.setBiddingInviteSuppliers(relevances);
        }

        Date endTime = vo.getEndTime();
        // 如果截止时间小于当前时间
        if (endTime.compareTo(new Date()) < 0) {
            // 结束了
            if (vo.getBiddingState() != 3) {
                lambdaUpdate().eq(BiddingPurchase::getBiddingId, vo.getBiddingId())
                        .set(BiddingPurchase::getBiddingState, 3)
                        .set(BiddingPurchase::getGmtModified, new Date()).update();
                vo.setBiddingState(3);
            }
        } else {
            // 没有到达结束时间
            // 判断是否通过并且发布了，修改状态为进行中
            if (vo.getState() == 5) {
                if (vo.getBiddingState() != 2) {
                    lambdaUpdate().eq(BiddingPurchase::getBiddingId, vo.getBiddingId())
                            .set(BiddingPurchase::getBiddingState, 2)
                            .set(BiddingPurchase::getGmtModified, new Date()).update();
                    vo.setBiddingState(2);
                }
            }
        }
        return vo;
    }


    private List<AuditRecords> getAuditRecords(BiddingPurchase biddingPurchase) {

        List<AuditRecords> auditRecordsList = new ArrayList<>();
//        ProcessConfig processConfig = processConfigService.getById(ProcessConstants.PUBLISH_BIDDING_PROCESS_ID);
        List<ProcessInstance> processInstances = processInstanceService.lambdaQuery().eq(ProcessInstance::getBusinessKey, biddingPurchase.getBiddingSn()).list();
        if (!processInstances.isEmpty()) {
            ProcessInstance instance = processInstances.get(0);
            List<AuditRecords> records = auditService.findRecordsBy(instance.getProcessInstanceId());
            auditRecordsList.addAll(records);
        }
        return auditRecordsList;

    }

    /**
     * 根据竞价id追加竞价明细
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBidingOrderItemsByBiddingId(CreateBidingOrderItemsByBiddingIdDTO dto) {
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        // 检验是自营店
        String shopId = user.getShopId();
        Shop shop = shopService.lambdaQuery().eq(Shop::getShopId, shopId)
                .select(Shop::getIsBusiness).one();
        if (shop == null) {
            throw new BusinessException("未找到店铺！");
        }
        if (shop.getIsBusiness() != 1) {
            throw new BusinessException("店铺不是自营店不能设置为待竞价！");
        }

        List<OrderItem> list = dto.getOrderItems();
        if (!CollectionUtils.isEmpty(list)) {
            String orderId = list.get(0).getOrderId();
            Orders orders = ordersService.lambdaQuery().eq(Orders::getOrderId, orderId).select(Orders::getState).one();
            if (orders.getState() != OrderEnum.STATE_FINISH.getCode()) {
                // 不是待发货
                throw new BusinessException("只有待发货订单，拒绝操作！");
            }
            for (OrderItem orderItem : list) {
                BiddingProduct bidP = new BiddingProduct();
                BeanUtils.copyProperties(orderItem, bidP);
                bidP.setBiddingId(dto.getBiddingId());
                bidP.setBiddingSn(dto.getBiddingSn());
                bidP.setBrand(orderItem.getBrandName());
                bidP.setClassId(orderItem.getClassId());
                bidP.setSpec(orderItem.getSkuName());
                bidP.setNum(orderItem.getBuyCounts());
                bidP.setCreateOrgId(user.getEnterpriseId());
                bidP.setCreateOrgName(user.getEnterpriseName());
                bidP.setProductTexture(orderItem.getTexture());
                bidP.setState(null);
                biddingProductService.save(bidP);
            }
            List<String> collect = list.stream().map(t -> t.getOrderItemId()).collect(Collectors.toList());
            // 修改主订单明细状态
            orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId, collect)
                    .set(OrderItem::getState, 4)
                    .set(OrderItem::getGmtModified, new Date()).update();
        }

    }

    /**
     * 根据竞价明细修改明细信息
     *
     * @param dtos
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateBiddingItemInfo(List<BatchUpdateBiddingItemInfoDTO> dtos) {
        for (BatchUpdateBiddingItemInfoDTO dto : dtos) {
            Integer count = lambdaQuery().eq(BiddingPurchase::getBiddingId, dto.getBiddingId())
                    .in(BiddingPurchase::getState, 0, 2).count();
            if (count == 0) {
                continue;
            }
            BiddingProduct biddingProduct = new BiddingProduct();
            BeanUtils.copyProperties(dto, biddingProduct);
            biddingProductService.update(biddingProduct);
        }
    }

    /**
     * 提交审核竞价信息
     *
     * @param bidingIds
     */
    @Override
    public void submitBidingByIds(List<String> bidingIds) {
        lambdaUpdate().in(BiddingPurchase::getBiddingId, bidingIds)
                .in(BiddingPurchase::getState, 0, 2)
                .set(BiddingPurchase::getState, 1).update();
    }

    /**
     * 查询发布竞价列表-平台
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils listAllCreateBiding(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> q) {
        String keywords = (String) jsonObject.get("keywords");
        String biddingSn = (String) jsonObject.get("biddingSn");
        String title = (String) jsonObject.get("title");
        String startDate = (String) jsonObject.get("startDate");
        String endDate = (String) jsonObject.get("endDate");
        String linkName = (String) jsonObject.get("linkName");
        String linkPhone = (String) jsonObject.get("linkPhone");
        Integer state = (Integer) jsonObject.get("state");
        List<Integer> states = (List<Integer>) jsonObject.get("states");
        Integer orderBy = (Integer) jsonObject.get("orderBy");
        String createStartDate = (String) jsonObject.get("createStartDate");
        String createEndDate = (String) jsonObject.get("createEndDate");
        Integer biddingState = (Integer) jsonObject.get("biddingState");
        Integer publicityState = (Integer) jsonObject.get("publicityState");
        if (orderBy != null) {
            if (orderBy == 1) {
                q.orderByDesc(BiddingPurchase::getGmtCreate);
            }
            if (orderBy == 2) {
                q.orderByDesc(BiddingPurchase::getStartTime);
            }
            if (orderBy == 3) {
                q.orderByDesc(BiddingPurchase::getEndTime);
            }
        }
        q.select(BiddingPurchase.class, f -> {
            return !f.getProperty().equals("biddingExplain") && !f.getProperty().equals("remarks");
        });
        q.in(!CollectionUtils.isEmpty(states), BiddingPurchase::getState, states);
        q.eq(state != null, BiddingPurchase::getState, state);
        q.eq(biddingState != null, BiddingPurchase::getBiddingState, biddingState);
        q.eq(publicityState != null, BiddingPurchase::getPublicityState, publicityState);
        if (StringUtils.isNotBlank(biddingSn)) q.like(BiddingPurchase::getBiddingSn, biddingSn.trim());
        if (StringUtils.isNotBlank(title)) q.like(BiddingPurchase::getTitle, title.trim());
        if (StringUtils.isNotBlank(linkName)) q.like(BiddingPurchase::getLinkName, linkName.trim());
        if (StringUtils.isNotBlank(linkPhone)) q.like(BiddingPurchase::getLinkPhone, linkPhone.trim());
        q.between(StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate), BiddingPurchase::getEndTime, startDate, endDate);
        q.between(StringUtils.isNotEmpty(createStartDate) && StringUtils.isNotEmpty(createEndDate), BiddingPurchase::getGmtCreate, createStartDate, createEndDate);
        if (!org.springframework.util.StringUtils.isEmpty(keywords)) {
            q.and((t) -> {
                t.like(BiddingPurchase::getBiddingSn, keywords)
                        .or()
                        .like(BiddingPurchase::getTitle, keywords);
            });
        }
        q.orderByDesc(BiddingPurchase::getGmtCreate);
        IPage<BiddingPurchase> page = this.page(
                new Query<BiddingPurchase>().getPage(jsonObject),
                q
        );
        List<BiddingPurchase> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageUtils(page);
        } else {
            for (BiddingPurchase record : records) {
                Date endTime = record.getEndTime();
                // 如果截止时间小于当前时间
                if (endTime.compareTo(new Date()) < 0) {
                    // 结束了
                    if (record.getBiddingState() != 3) {
                        lambdaUpdate().eq(BiddingPurchase::getBiddingId, record.getBiddingId())
                                .set(BiddingPurchase::getBiddingState, 3)
                                .set(BiddingPurchase::getGmtModified, new Date()).update();
                        record.setBiddingState(3);
                    }
                } else {
                    // 没有到达结束时间
                    // 判断是否通过并且发布了，修改状态为进行中
                    if (record.getState() == 5) {
                        if (record.getBiddingState() != 2) {
                            lambdaUpdate().eq(BiddingPurchase::getBiddingId, record.getBiddingId())
                                    .set(BiddingPurchase::getBiddingState, 2)
                                    .set(BiddingPurchase::getGmtModified, new Date()).update();
                            record.setBiddingState(2);
                        }
                    }
                }
            }
            return new PageUtils(page);
        }
    }

    /**
     * 审核竞价
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditBidingInfo(AuditBidingInfoDTO dto) {
        List<String> roles = ThreadLocalUtil.getCurrentUser().getRoles();
        boolean isCheckAuth = roles.contains("物资竞价发布审核");
        if (!isCheckAuth) {
            throw new BusinessException("没有审核权限！");
        }
        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        String biddingSn = dto.getBiddingSn();
        BiddingPurchase bid = lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn)
                .select(BiddingPurchase::getState, BiddingPurchase::getType).one();
        if (bid != null && bid.getState() == 1) {
            Integer isOpen = dto.getIsOpen();
            if (isOpen == 1) {
//                AuditRecord auditRecord = new AuditRecord();
//                auditRecord.setRelevanceType(3);
//                auditRecord.setRelevanceId(biddingId);
//                auditRecord.setResultType(1);
//                auditRecord.setAuditType(1);
//                auditRecord.setAuditResult("【同意】");
//                auditRecordService.create(auditRecord);
                //开启审核流程,审核通过
                processConfigService.myFunc(ProcessConstants.PUBLISH_BIDDING_PROCESS_ID,
                        currentUser, 1, dto.getBiddingSn(), "【同意】");
                // 审核通过
                lambdaUpdate().eq(BiddingPurchase::getBiddingSn, biddingSn)
                        .set(BiddingPurchase::getState, 5)
                        .set(BiddingPurchase::getStartTime, new Date())
                        .set(BiddingPurchase::getGmtModified, new Date()).update();
                // TODO  邀请竞价 审核通过 发送站内信
                if (bid.getType() == 2) {
                    sendStationMessage(biddingSn);
                }

            }
            if (isOpen == 0) {
//                AuditRecord auditRecord = new AuditRecord();
//                String auditResult = dto.getAuditResult();
//                auditRecord.setRelevanceType(3);
//                auditRecord.setRelevanceId(biddingId);
//                auditRecord.setResultType(2);
//                auditRecord.setAuditType(1);
//                auditRecord.setAuditResult("【拒绝】" + auditResult);
//                auditRecordService.create(auditRecord);

                //开启审核流程,审核不通过
                processConfigService.myFunc(ProcessConstants.PUBLISH_BIDDING_PROCESS_ID,
                        currentUser, 2, dto.getBiddingSn(), "【拒绝】" + dto.getAuditResult());
                // 审核不通过
                lambdaUpdate().eq(BiddingPurchase::getBiddingSn, biddingSn)
                        .set(BiddingPurchase::getState, 2)
                        .set(BiddingPurchase::getGmtModified, new Date()).update();
                //// 修改清单状态
                //List<BiddingProduct> productList = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingId, biddingId)
                //        .list();
                //if (!CollectionUtils.isEmpty(productList)){
                //    List<String> collect = productList.stream().map(BiddingProduct::getSynthesizeTemporaryDtlId).collect(Collectors.toList());
                //    synthesizeTemporaryDtlService.lambdaUpdate()
                //            .in(!CollectionUtils.isEmpty(collect),SynthesizeTemporaryDtl::getSynthesizeTemporaryDtlId,collect)
                //            .set(SynthesizeTemporaryDtl::getIsBidding,0)
                //            .update();
                //}
            }
        }
    }

    /**
     * 平台获取竞价详细信息
     *
     * @param biddingSn
     * @return
     */
    @Override
    public PlatformBiddingPurchaseInfoVO getPlatformBiddingPurchaseInfo(String biddingSn) {
        BiddingPurchase biddingPurchase = lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn).one();
        PlatformBiddingPurchaseInfoVO vo = new PlatformBiddingPurchaseInfoVO();
        if (biddingPurchase == null) {
            return vo;
        }
        // 获取主体信息
        BeanUtils.copyProperties(biddingPurchase, vo);

        List<BiddingProduct> list = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingSn, biddingSn).list();
        vo.setBiddingProducts(list);

        // 获取审核历史
        List<AuditRecord> auditRecords = auditRecordService.lambdaQuery()
                .eq(AuditRecord::getRelevanceType, 3)
                .orderByDesc(AuditRecord::getGmtCreate)
                .eq(AuditRecord::getRelevanceId, biddingPurchase.getBiddingId()).list();
        vo.setAuditRecords(auditRecords);


        // 获取报价历史
        LambdaQueryChainWrapper<BiddingBidRecord> rq = biddingBidRecordService.lambdaQuery()
                .eq(BiddingBidRecord::getBiddingSn, biddingSn)
                .ne(BiddingBidRecord::getState, 0)
                .orderByDesc(BiddingBidRecord::getGmtCreate);
        if (biddingPurchase.getBiddingState() != 3) {
            rq.select(BiddingBidRecord.class, f -> {
                return !f.getProperty().equals("bidAmount") && !f.getProperty().equals("bidRateAmount");
            });
        }
        List<BiddingBidRecord> biddingBidRecords = rq.list();
        vo.setBiddingBidRecords(biddingBidRecords);
        return vo;
    }

    /**
     * 竞价公示列表
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils biddingPageList(JSONObject jsonObject, LambdaQueryWrapper<BiddingPurchase> q) {
        String keywords = (String) jsonObject.get("keywords");

        q.select(BiddingPurchase.class, f -> {
            return !f.getProperty().equals("biddingExplain") && !f.getProperty().equals("remarks");
        });
        if (!org.springframework.util.StringUtils.isEmpty(keywords)) {
            q.and((t) -> {
                t.like(BiddingPurchase::getBiddingSn, keywords)
                        .or()
                        .like(BiddingPurchase::getTitle, keywords);
            });
        }
        // 只查询审核通过的数据
        q.in(BiddingPurchase::getState, 4, 7, 8, 9, 6, 10);
//        q.eq(BiddingPurchase::getState, 5);
        q.orderByDesc(BiddingPurchase::getGmtCreate);
        IPage<BiddingPurchase> page = this.page(
                new Query<BiddingPurchase>().getPage(jsonObject),
                q
        );
        List<BiddingPurchase> records = page.getRecords();

        ArrayList<BiddingPageListVO> biddingPageListVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return new PageUtils(page);
        } else {
            for (BiddingPurchase record : records) {
                Date endTime = record.getEndTime();
                // 如果截止时间小于当前时间
                if (endTime.compareTo(new Date()) < 0) {
                    // 结束了
                    if (record.getBiddingState() != 3) {
                        lambdaUpdate().eq(BiddingPurchase::getBiddingId, record.getBiddingId())
                                .set(BiddingPurchase::getBiddingState, 3)
                                .set(BiddingPurchase::getGmtModified, new Date()).update();
                        record.setBiddingState(3);
                    }
                } else {
                    // 没有到达结束时间 修改状态为进行中
                    if (record.getBiddingState() != 2) {
                        lambdaUpdate().eq(BiddingPurchase::getBiddingId, record.getBiddingId())
                                .set(BiddingPurchase::getBiddingState, 2)
                                .set(BiddingPurchase::getGmtModified, new Date()).update();
                        record.setBiddingState(2);
                    }
                }
                BiddingPageListVO biddingPageListVO = new BiddingPageListVO();
                BeanUtils.copyProperties(record, biddingPageListVO);
                biddingPageListVOS.add(biddingPageListVO);
            }
            PageUtils pageUtils = new PageUtils(page);
            pageUtils.setList(biddingPageListVOS);
            return pageUtils;
        }


    }

    /**
     * 查询竞价详情
     *
     * @param biddingSn
     * @return
     */
    @Override
    public BiddingPurchaseAndItemVO getBidingDetail(String biddingSn) {
        BiddingPurchase biddingPurchase = lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn).one();
        BiddingPurchaseAndItemVO vo = new BiddingPurchaseAndItemVO();
        if (biddingPurchase == null) {
            return vo;
        }
        if (biddingPurchase.getBiddingState() == 1) {
            return vo;
        }
        BeanUtils.copyProperties(biddingPurchase, vo);
        List<BiddingProduct> biddingProducts = biddingProductService.lambdaQuery()
                .eq(BiddingProduct::getBiddingSn, biddingSn).list();

        List<BiddingPurchaseItemVO> vos = new ArrayList<>();
        for (BiddingProduct biddingProduct : biddingProducts) {
            BiddingPurchaseItemVO biddingPurchaseItemVO = new BiddingPurchaseItemVO();
            BeanUtils.copyProperties(biddingProduct, biddingPurchaseItemVO);
            vos.add(biddingPurchaseItemVO);
        }
        vo.setVos(vos);
        return vo;
    }

    /**
     * 参与竞价
     *
     * @param biddingSn
     */
    @Override
    public void putBidingSupplierInfo(String biddingSn) {
        BiddingPurchase biddingPurchase = lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn).one();
        if (biddingPurchase == null) {
            throw new BusinessException("竞价信息不存在！");
        }
        Integer biddingState = biddingPurchase.getBiddingState();
        if (biddingState != 2) {
            throw new BusinessException("竞价未开始或已结束不能报名！");
        }
        BiddingSuppliers s = new BiddingSuppliers();
        s.setBiddingId(biddingPurchase.getBiddingId());
        s.setBiddingSn(biddingPurchase.getBiddingSn());
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        if (StringUtils.isBlank(enterpriseId)) {
            throw new BusinessException("请重新登陆！");
        }
        Integer count = biddingSuppliersService.lambdaQuery()
                .eq(BiddingSuppliers::getBiddingSn, biddingSn)
                .eq(BiddingSuppliers::getSupplierId, enterpriseId)
                .eq(BiddingSuppliers::getType, 1)
                .count();
        if (count > 0) {
            throw new BusinessException("您已参与竞价，无需重复参与");
        }
        EnterpriseInfo en = enterpriseInfoService.lambdaQuery().eq(EnterpriseInfo::getEnterpriseId, enterpriseId)
                .select(EnterpriseInfo::getEnterpriseName, EnterpriseInfo::getAdminName, EnterpriseInfo::getAdminPhone, EnterpriseInfo::getIsSupplier).one();
        if (en == null) {
            throw new BusinessException("供应商信息不存在！");
        }
        if (en.getIsSupplier() == null || en.getIsSupplier() != 2) {
            throw new BusinessException("不是供应商！");
        }
        // 邀请竞价
        if (biddingPurchase.getType() == 2) {
            Integer isInvite = biddingInvitationRelevanceService.lambdaQuery().eq(BiddingInvitationRelevance::getBiddingSn, biddingPurchase.getBiddingSn())
                    .eq(BiddingInvitationRelevance::getSupplierId, enterpriseId).count();
            if (isInvite == 0 || isInvite == null) {
                throw new BusinessException("不是被邀请供应商，无法报名！");
            }

        }
        s.setSupplierId(enterpriseId);
        s.setSupplierName(en.getEnterpriseName());
        s.setContactPerson(en.getAdminName());
        s.setContactPhone(en.getAdminPhone());
        s.setType(1);
        s.setApplyAffirmTime(new Date());
        biddingSuppliersService.save(s);
    }

    /**
     * 检查是否参与竞价
     *
     * @param biddingSn
     */
    @Override
    public void checkIsBiddingSupplier(String biddingSn) {
        Integer count = biddingSuppliersService.lambdaQuery()
                .eq(BiddingSuppliers::getBiddingSn, biddingSn)
                .eq(BiddingSuppliers::getSupplierId, ThreadLocalUtil.getCurrentUser().getEnterpriseId())
                .eq(BiddingSuppliers::getType, 1)
                .count();
        if (count == 0) {
            throw new BusinessException("未参与竞价！不能进行报价操作！");
        }
    }

    /**
     * 中标提交审核
     *
     * @param bidRecordId
     * @param biddingId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void isHitBidingSubmit(String bidRecordId, String biddingId) {
        BiddingPurchase biddingPurchase = lambdaQuery().eq(BiddingPurchase::getBiddingId, biddingId)
                .select(BiddingPurchase::getBiddingState).one();
        Integer biddingState = biddingPurchase.getBiddingState();
        if (biddingState != 3) {
            throw new BusinessException("竞价未结束不能提交中标审核！");
        }
        lambdaUpdate().eq(BiddingPurchase::getBiddingId, biddingId)
                .set(BiddingPurchase::getState, 6)
                .set(BiddingPurchase::getGmtModified, new Date()).update();
        biddingBidRecordService.lambdaUpdate()
                .eq(BiddingBidRecord::getBidRecordId, bidRecordId)
                .set(BiddingBidRecord::getState, 5)
                .set(BiddingBidRecord::getGmtModified, new Date()).update();
    }

    @Autowired
    ProcessNodeService processNodeService;

    /**
     * 中标审核竞价
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditHitBidding(AuditBidingInfoDTO dto) {
        List<String> roles = ThreadLocalUtil.getCurrentUser().getRoles();
        boolean isCheckAuth = roles.contains("物资竞价中标审核");
        if (!isCheckAuth) {
            throw new BusinessException("没有审核权限！");
        }
        String biddingId = dto.getBiddingId();
//        String bidRecordId = dto.getBidRecordId();

        UserLogin currentUser = ThreadLocalUtil.getCurrentUser();
        BiddingPurchase bid = lambdaQuery().eq(BiddingPurchase::getBiddingId, biddingId)
                .select(BiddingPurchase.class, f -> {
                    return !f.getProperty().equals("biddingExplain") && !f.getProperty().equals("remarks");
                }).one();
        if (bid != null && bid.getState() == 6) {
            Integer isOpen = dto.getIsOpen();
            // 通过
            if (isOpen == 1) {
//                AuditRecord auditRecord = new AuditRecord();
//                auditRecord.setRelevanceType(4);
//                auditRecord.setRelevanceId(bidRecordId);
//                auditRecord.setResultType(1);
//                auditRecord.setAuditType(6);
//                auditRecord.setAuditResult("【同意】");
//                auditRecordService.create(auditRecord);

                List<ProcessNode> processNodes = new ArrayList<>();
                ProcessWorkflowResult processWorkflowResult = null;
                //开启审核流程,审核通过
                if (dto.getAuditProcessType() == 1) {
                    processWorkflowResult = processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_FIRST,
                            currentUser, 1, dto.getBiddingId(), "【同意】" + dto.getAuditResult());
                    //查看是否是最终审核；如果不是，庄改改为审核中
                    processNodes = processNodeService.lambdaQuery().eq(ProcessNode::getProcessId,
                            ProcessConstants.BIDDING_PROCESS_ID_FIRST).orderByDesc(ProcessNode::getSort).list();
                } else {
                    processWorkflowResult = processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_NO_FIRST,
                            currentUser, 1, dto.getBiddingId(), "【同意】" + dto.getAuditResult());

                    //查看是否是最终审核；如果不是，庄改改为审核中
                    processNodes = processNodeService.lambdaQuery().eq(ProcessNode::getProcessId,
                            ProcessConstants.BIDDING_PROCESS_ID_NO_FIRST).orderByDesc(ProcessNode::getSort).list();
                }
                ProcessWorkflowResult finalProcessWorkflowResult = processWorkflowResult;
                List<ProcessNode> collect = processNodes.stream().filter(e -> e.getProcessNodeId().equals(finalProcessWorkflowResult.getCurrentNodeId())).collect(Collectors.toList());

                // 审核通过
                if (processNodes.size() == 3){
                    lambdaUpdate().eq(BiddingPurchase::getBiddingId, biddingId)
                            .set(BiddingPurchase::getState, collect.size() > 0 && collect.get(0).getSort() == 3 ? 7 : 6)
                            .set(BiddingPurchase::getGmtModified, new Date()).update();
                }else {
                    lambdaUpdate().eq(BiddingPurchase::getBiddingId, biddingId)
                            .set(BiddingPurchase::getState, 7)
                            .set(BiddingPurchase::getGmtModified, new Date()).update();
                }

//                biddingBidRecordService.lambdaUpdate().eq(BiddingBidRecord::getBidRecordId, bidRecordId)
//                        .set(BiddingBidRecord::getState, 6)
//                        .set(BiddingBidRecord::getGmtModified, new Date()).update();
                BiddingBidRecord biddingBidRecord = biddingBidRecordService.lambdaQuery().eq(BiddingBidRecord::getBiddingId, biddingId)
                        .eq(BiddingBidRecord::getBidState, 1).one();

                // 保存中标记录
                BiddingWinRecord bw = new BiddingWinRecord();
                bw.setBidRecordId(biddingBidRecord.getBidRecordId());
                bw.setBiddingId(biddingId);
                bw.setSynthesizeTemporarySn(bid.getSynthesizeTemporarySn());
                bw.setSupplierId(biddingBidRecord.getSupplierId());
                bw.setSupplierName(biddingBidRecord.getSupplierName());
                bw.setBidAmount(biddingBidRecord.getBidAmount());
                bw.setBidRateAmount(biddingBidRecord.getBidRateAmount());
                bw.setWinTime(new Date());
                bw.setCreateOrgId(ThreadLocalUtil.getCurrentUser().getEnterpriseId());
                bw.setCreateOrgName(ThreadLocalUtil.getCurrentUser().getEnterpriseName());
                biddingWinRecordService.save(bw);

                // 根据类型生成不同订单
                // 清单无需生成订单只需要改变状态
                BiddingPurchase purchase = getById(biddingId);
                if (purchase.getBiddingSourceType() == 3) {
                    //修改清单状态
                    SynthesizeTemporary synthesizeTemporary = synthesizeTemporaryService.lambdaQuery().eq(SynthesizeTemporary::getSynthesizeTemporarySn,
                            purchase.getSynthesizeTemporarySn()).one();
                    synthesizeTemporary.setState(2);
                    synthesizeTemporaryService.updateById(synthesizeTemporary);
                    return;
                }
                // 生成订单
                batchCreateTwoOrder(dto, biddingBidRecord);

                ShopSupplierRele shopSupplierRele = new ShopSupplierRele();
                shopSupplierRele.setShopId(bid.getShopId());
                shopSupplierRele.setPermissionsTurnover("0");
                shopSupplierRele.setSupplierId(biddingBidRecord.getSupplierId());
                shopSupplierRele.setSupplierName(biddingBidRecord.getSupplierName());
                Integer count = shopSupplierReleService.lambdaQuery().eq(ShopSupplierRele::getShopId, bid.getShopId())
                        .eq(ShopSupplierRele::getSupplierId, biddingBidRecord.getSupplierId()).count();
                if (count == 0) {
                    shopSupplierReleService.save(shopSupplierRele);
                }

                //发送站内信息
                // 查询被邀请的供应商
                StationMessageReceiveVO msg = getStationMessageReceiveVO(purchase, bid.getType() == 1 ? "公开竞价" : "邀请竞价");
                msg.setContent("根据" + purchase.getStartTime() + purchase.getTitle() + "竞价项目的竞价结果，贵单位被我公司确定为成交人");
                stationMessageService.createBatch(msg);

            }
            // 不通过
            if (isOpen == 0) {
//                AuditRecord auditRecord = new AuditRecord();
//                String auditResult = dto.getAuditResult();
//                auditRecord.setRelevanceType(4);
//                auditRecord.setRelevanceId(bidRecordId);
//                auditRecord.setResultType(2);
//                auditRecord.setAuditType(6);
//                auditRecord.setAuditResult("【拒绝】" + auditResult);
//                auditRecordService.create(auditRecord);

                //开启审核流程,审核不通过
//                processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_FIRST,
//                        currentUser, 2, dto.getBiddingId(), "【拒绝】" + dto.getAuditResult());

                if (dto.getAuditProcessType() == 1) {
                    processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_FIRST,
                            currentUser, 2, dto.getBiddingId(), "【拒绝】" + dto.getAuditResult());
                } else {
                    processConfigService.myFunc(ProcessConstants.BIDDING_PROCESS_ID_NO_FIRST,
                            currentUser, 2, dto.getBiddingId(), "【拒绝】" + dto.getAuditResult());
                }

                // 审核不通过
                lambdaUpdate().eq(BiddingPurchase::getBiddingId, biddingId)
                        .set(BiddingPurchase::getState, 4)
                        .set(BiddingPurchase::getGmtModified, new Date()).update();
//                biddingBidRecordService.lambdaUpdate().eq(BiddingBidRecord::getBidRecordId, biddingBidRecord)
//                        .set(BiddingBidRecord::getState, 7)
//                        .set(BiddingBidRecord::getGmtModified, new Date()).update();

                StationMessageReceiveVO msg = getStationMessageReceiveVO(bid, bid.getType() == 1 ? "公开竞价" : "邀请竞价");
                msg.setContent("根据" + bid.getStartTime() + bid.getTitle() + "竞价项目的竞价结果，贵单位遗憾未能中选");
                stationMessageService.createBatch(msg);
            }
        }

    }

    @NotNull
    private StationMessageReceiveVO getStationMessageReceiveVO(BiddingPurchase purchase, String s) {
        ArrayList<String> ids = new ArrayList<>();
        List<BiddingInvitationRelevance> list = biddingInvitationRelevanceService.lambdaQuery()
                .eq(BiddingInvitationRelevance::getBiddingSn, purchase.getBiddingSn()).list();
        if (CollectionUtils.isEmpty(list)) {
            List<BiddingBidRecord> bidRecords = biddingBidRecordService.lambdaQuery().eq(BiddingBidRecord::getBiddingId, purchase.getBiddingId())
                    .eq(BiddingBidRecord::getBidState, 1).list();
            ids.addAll(bidRecords.stream().map(BiddingBidRecord::getSupplierId).collect(Collectors.toList()));
        }

        ids.addAll(list.stream().map(BiddingInvitationRelevance::getSupplierId).collect(Collectors.toList()));
        StationMessageReceiveVO msg = new StationMessageReceiveVO();
        msg.setEnterpriseIdList(ids);
        // 标题
        msg.setTitle(s);
        return msg;
    }

    /**
     * 流标
     *
     * @param biddingId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loseEfficacyBidding(String biddingId) {
        BiddingPurchase bid = lambdaQuery().eq(BiddingPurchase::getBiddingId, biddingId)
                .select(BiddingPurchase::getState, BiddingPurchase::getBiddingState).one();
        if ((bid.getState() == 5 || bid.getState() == 8) && bid.getBiddingState() == 3) {
            lambdaUpdate().eq(BiddingPurchase::getBiddingId, biddingId)
                    .set(BiddingPurchase::getState, 9)
                    .set(BiddingPurchase::getGmtModified, new Date()).update();
            // 订单都改为待竞价状态
            List<BiddingProduct> biddingProducts = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingId, biddingId).list();
            for (BiddingProduct biddingProduct : biddingProducts) {
                // 区分订单竞价和清单竞价
                // 订单竞价
                if (StringUtils.isNotBlank(biddingProduct.getOrderItemId())) {
                    OrderItem orderItem = orderItemService.getById(biddingProduct.getOrderItemId());
                    Integer state = orderItem.getState();
                    if (state == 4) {
                        orderItem.setState(3);
                        orderItemService.update(orderItem);
                    }
                }
                // 清单竞价
                else {
                    SynthesizeTemporaryDtl temporaryDtl = synthesizeTemporaryDtlService.getById(biddingProduct.getSynthesizeTemporaryDtlId());
                    if (temporaryDtl != null && temporaryDtl.getIsBidding() == 1) {
                        temporaryDtl.setIsBidding(0);
                        synthesizeTemporaryDtlService.updateById(temporaryDtl);
                    }
                }

            }

        }
    }

    /**
     * 根据竞价id删除竞价明细
     *
     * @param biddingProductIds
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBidingOrderItemsByBiddingId(List<String> biddingProductIds) {
        BiddingProduct biddingProduct11 = biddingProductService.getById(biddingProductIds.get(0));
        String biddingId = biddingProduct11.getBiddingId();
        BiddingPurchase biddingPurchase = getById(biddingId);
        Integer state = biddingPurchase.getState();
        if (state == 0 || state == 2) {
            List<BiddingProduct> list = biddingProductService.lambdaQuery().in(BiddingProduct::getBiddingProductId, biddingProductIds).list();
            for (BiddingProduct biddingProduct : list) {
                biddingProductService.delete(biddingProduct.getBiddingProductId());
                // 修改订单明细状态
                orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId, biddingProduct.getOrderItemId())
                        .set(OrderItem::getState, 3)
                        .set(OrderItem::getGmtModified, new Date()).update();
                // 修改清单明细
                SynthesizeTemporaryDtl temporaryDtl = synthesizeTemporaryDtlService.getById(biddingProduct.getSynthesizeTemporaryDtlId());
                if (temporaryDtl != null && temporaryDtl.getIsBidding() == 1) {
                    temporaryDtl.setIsBidding(0);
                    synthesizeTemporaryDtlService.updateById(temporaryDtl);
                }
            }
        } else {
            throw new BusinessException("拒接删除！");
        }
    }

    /**
     * 删除竞价
     *
     * @param biddingId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBidingByBiddingId(String biddingId) {
        BiddingPurchase biddingPurchase = getById(biddingId);
        Integer state = biddingPurchase.getState();
        if (state == 0 || state == 2) {
            List<BiddingProduct> list = biddingProductService.lambdaQuery().eq(BiddingProduct::getBiddingId, biddingId).list();
            for (BiddingProduct biddingProduct : list) {
                biddingProductService.delete(biddingProduct.getBiddingProductId());
                // 修改订单明细状态
                orderItemService.lambdaUpdate().in(OrderItem::getOrderItemId, biddingProduct.getOrderItemId())
                        .set(OrderItem::getState, 3)
                        .set(OrderItem::getGmtModified, new Date()).update();
                // 修改清单状态
                synthesizeTemporaryDtlService.lambdaUpdate()
                        .eq(biddingProduct.getSynthesizeTemporaryDtlId() != null, SynthesizeTemporaryDtl::getSynthesizeTemporaryDtlId, biddingProduct.getSynthesizeTemporaryDtlId())
                        .set(SynthesizeTemporaryDtl::getIsBidding, 0)
                        .set(SynthesizeTemporaryDtl::getGmtModified, new Date()).update();
                // 邀请需要删除关联
                if (biddingPurchase.getType() == 2) {
                    biddingInvitationRelevanceService.lambdaUpdate().eq(BiddingInvitationRelevance::getBiddingSn, biddingPurchase.getBiddingSn())
                            .set(BiddingInvitationRelevance::getIsDelete, -1).update();
                }
            }
            delete(biddingId);
        } else {
            throw new BusinessException("拒接删除！");
        }
    }

    @Autowired
    MallConfig mallConfig;

    public void batchCreateTwoOrder(AuditBidingInfoDTO dto, BiddingBidRecord biddingBidRecord) {
        String biddingId = dto.getBiddingId();
        String bidRecordId = biddingBidRecord.getBidRecordId();
        BiddingPurchase biddingPurchase = getById(biddingId);
        Integer productType = biddingPurchase.getProductType();
        Integer billType = biddingPurchase.getBillType();

//        EnterpriseInfo en = enterpriseInfoService.lambdaQuery()
//                .eq(EnterpriseInfo::getEnterpriseId, biddingBidRecord.getSupplierId())
//                .select(EnterpriseInfo::getTaxRate,EnterpriseInfo::getEnterpriseId).one();
//        if(en == null) {
//            throw new BusinessException("供应商不存在！");
//        }
        BigDecimal taxRate = biddingBidRecord.getTaxRate();
        if (taxRate == null) {
            throw new BusinessException("供应商未设置税率！");
        }
        // 获取所有的订单商品
        List<BiddingProduct> biddingProducts = biddingProductService.lambdaQuery()
                .eq(BiddingProduct::getBiddingId, biddingId).list();
        // 根据订单分组
        Map<String, List<BiddingProduct>> bidProductGroup = biddingProducts.stream()
                .collect(Collectors.groupingBy(BiddingProduct::getOrderId));
        bidProductGroup.forEach((orderId, biddingProduct) -> {
            // 获取ids
            List<String> orderItemIds = biddingProduct.stream().map(t -> t.getOrderItemId()).collect(Collectors.toList());
            List<OrderItem> orderItems = orderItemService.lambdaQuery().in(OrderItem::getOrderItemId, orderItemIds).list();
            if (CollectionUtils.isEmpty(orderItems)) {
                throw new BusinessException("订单不存在！");
            }
            Orders orders = ordersService.getById(orderId);
            Orders newOrders = new Orders();
            BeanUtils.copyProperties(orders, newOrders);
            newOrders.setOrderId(null);
            newOrders.setUntitled(null);
            newOrders.setOrderSn(null);
            // 总成本价
            BigDecimal costPriceTotal = new BigDecimal(0);
            BigDecimal noRateCostPriceTotal = new BigDecimal(0);
            String untitled = "";

            HashMap<String, BigDecimal> oMap = new HashMap<>();
            // 拼接名称
            for (BiddingProduct biddingProduct1 : biddingProduct) {
                BiddingBidRecordItem b2 = biddingBidRecordItemService
                        .lambdaQuery()
                        .eq(BiddingBidRecordItem::getBidRecordId, bidRecordId)
                        .eq(BiddingBidRecordItem::getBiddingProductId, biddingProduct1.getBiddingProductId())
                        .one();
                untitled = untitled + biddingProduct1.getProductName() + ",";
                costPriceTotal = costPriceTotal.add(b2.getBidRateAmount());
                noRateCostPriceTotal = noRateCostPriceTotal.add(b2.getBidAmount());
                oMap.put(biddingProduct1.getOrderItemId() + "price", b2.getBidRatePrice());
                oMap.put(biddingProduct1.getOrderItemId() + "amount", b2.getBidRateAmount());
                oMap.put(biddingProduct1.getOrderItemId() + "priceNoRate", b2.getBidPrice());
                oMap.put(biddingProduct1.getOrderItemId() + "amountNoRate", b2.getBidAmount());
                if (productType == 1) {
                    // 如果是大宗临购
                    // 浮动
                    if (billType == 1) {
                        oMap.put(biddingProduct1.getOrderItemId() + "netPrice", b2.getNetPrice());
                        oMap.put(biddingProduct1.getOrderItemId() + "fixationPrice", b2.getFixationPrice());
                    }
                    if (billType == 2) {
                        oMap.put(biddingProduct1.getOrderItemId() + "outFactoryPrice", b2.getOutFactoryPrice());
                        oMap.put(biddingProduct1.getOrderItemId() + "transportPrice", b2.getTransportPrice());
                    }
                }
            }
            newOrders.setUntitled(untitled.substring(0, untitled.length() - 1));
            newOrders.setTaxRate(taxRate);
            newOrders.setActualAmount(costPriceTotal);
            if (mallConfig.isNotRateAmount == 1) {
                newOrders.setNoRateAmount(noRateCostPriceTotal);
            } else {
                newOrders.setNoRateAmount(TaxCalculator.calculateNotTarRateAmount(costPriceTotal, taxRate));
            }
            newOrders.setTotalAmount(BigDecimal.ZERO);
            newOrders.setCostPriceTotal(BigDecimal.ZERO);
            newOrders.setOrderClass(3);
            newOrders.setParentOrderId(orders.getOrderId());
            newOrders.setOrderSourceType(2);
            newOrders.setMasterAffirmState(0);
            newOrders.setAffirmState(0);
            newOrders.setOrderSourceId(bidRecordId);
            newOrders.setSupplierId(biddingBidRecord.getSupplierId());
            newOrders.setSupplierName(biddingBidRecord.getSupplierName());
            newOrders.setOrderSn(OrderUtils.getOrder());
            ordersService.save(newOrders);

            // 保存好了订单项，
            for (OrderItem newOrderItem : orderItems) {
                newOrderItem.setOrderId(newOrders.getOrderId());
                newOrderItem.setOrderSn(newOrders.getOrderSn());
                newOrderItem.setTaxRate(taxRate);
                BigDecimal price = oMap.get(newOrderItem.getOrderItemId() + "price");
                newOrderItem.setProductPrice(price);
                BigDecimal amount = oMap.get(newOrderItem.getOrderItemId() + "amount");
                newOrderItem.setTotalAmount(amount);
                BigDecimal priceNoRate = oMap.get(newOrderItem.getOrderItemId() + "priceNoRate");
                newOrderItem.setNoRatePrice(priceNoRate);
                BigDecimal amountNoRate = oMap.get(newOrderItem.getOrderItemId() + "amountNoRate");
                newOrderItem.setNoRateAmount(amountNoRate);
                // 不含税
                if (productType == 1) {
                    if (billType == 1) {
                        BigDecimal netPrice = oMap.get(newOrderItem.getOrderItemId() + "netPrice");
                        BigDecimal fixationPrice = oMap.get(newOrderItem.getOrderItemId() + "fixationPrice");
                        newOrderItem.setNetPrice(netPrice);
                        newOrderItem.setFixationPrice(fixationPrice);
                    }
                    if (billType == 2) {
                        BigDecimal outFactoryPrice = oMap.get(newOrderItem.getOrderItemId() + "outFactoryPrice");
                        BigDecimal transportPrice = oMap.get(newOrderItem.getOrderItemId() + "transportPrice");
                        newOrderItem.setOutFactoryPrice(outFactoryPrice);
                        newOrderItem.setTransportPrice(transportPrice);
                    }
                }
                newOrderItem.setCostPrice(BigDecimal.ZERO);
                newOrderItem.setCostAmount(BigDecimal.ZERO);
                newOrderItem.setOriginalPrice(BigDecimal.ZERO);
                // 保存父明细id
                newOrderItem.setParentOrderItemId(newOrderItem.getOrderItemId());
                newOrderItem.setOrderItemId(null);
                orderItemService.save(newOrderItem);

                // 修改父级明细价格
                String parentOrderItemId = newOrderItem.getParentOrderItemId();
                OrderItem orderItemOne = orderItemService.getById(parentOrderItemId);
                orderItemOne.setCostPrice(oMap.get(parentOrderItemId + "price"));
                orderItemOne.setCostAmount(oMap.get(parentOrderItemId + "amount"));
                orderItemService.update(orderItemOne);
            }

            // 主订单重新计算
            Orders orderOne = ordersService.getById(orderId);
            // 所有明细
            List<OrderItem> orderItemsOne = orderItemService.lambdaQuery()
                    .eq(OrderItem::getOrderId, orderOne.getOrderId()).list();
            BigDecimal costAmount = new BigDecimal(0);
            for (OrderItem orderItem : orderItemsOne) {
                // 计算总成本价
                BigDecimal c = orderItem.getCostAmount();
                costAmount = costAmount.add(c);
            }
            orderOne.setCostPriceTotal(costAmount);
            ordersService.update(orderOne);
        });


    }

    /**
     * TODO 竞价审核通过，站内信邀请供应商
     */
    private void sendStationMessage(String biddingSn) {
        // 竞价数据
        BiddingPurchase purchase = lambdaQuery().eq(BiddingPurchase::getBiddingSn, biddingSn).one();
        if (purchase == null) {
            throw new BusinessException("竞价信息不存在！");
        }
        // 查询被邀请的供应商
        List<BiddingInvitationRelevance> list = biddingInvitationRelevanceService.lambdaQuery()
                .eq(BiddingInvitationRelevance::getBiddingSn, purchase.getBiddingSn()).list();
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException("没有被邀请的供应商！");
        }

        StationMessageReceiveVO msg = new StationMessageReceiveVO();
        msg.setEnterpriseIdList((ArrayList<String>) list.stream().map(BiddingInvitationRelevance::getSupplierId).collect(Collectors.toList()));
        // 标题
        msg.setTitle("邀请竞价");
        // 内容  根据XX号XX竞价项目的竞价结果，贵单位被我公司确定为成交人；根据XX号XX竞价项目的竞价结果，贵单位遗憾未能中选
        msg.setContent(purchase.getCreateOrgName() + "邀请您参与竞价：" + purchase.getTitle());
        stationMessageService.createBatch(msg);
    }

    @Override
    public void createInventoryBidingByBiddingId(CreateBidingOrderItemsByBiddingIdDTO dto) {
        //  清单明细
        List<SynthesizeTemporaryDtl> synthesizeTemporaryDtls = dto.getSynthesizeTemporaryDtls();
        if (CollectionUtils.isEmpty(synthesizeTemporaryDtls)) {
            return;
        }
        BiddingPurchase bid = lambdaQuery().eq(BiddingPurchase::getBiddingId, dto.getBiddingId()).one();
        if (bid == null) {
            throw new BusinessException("竞价信息不存在！");
        }
        UserLogin user = ThreadLocalUtil.getCurrentUser();
        SynthesizeTemporaryDtl temporaryDtl = synthesizeTemporaryDtls.get(0);
        String synthesizeTemporaryId = temporaryDtl.getSynthesizeTemporaryId();
        SynthesizeTemporary temporary = synthesizeTemporaryService.getById(synthesizeTemporaryId);

        List<BiddingProduct> products = new ArrayList<>();
        for (SynthesizeTemporaryDtl dtl : synthesizeTemporaryDtls) {
            BiddingProduct bidP = new BiddingProduct();
            BeanUtils.copyProperties(dtl, bidP);
            bidP.setBiddingId(bid.getBiddingId());
            bidP.setBiddingSn(bid.getBiddingSn());
            bidP.setReferencePrice(dtl.getMaxPrice());
            bidP.setClassPathName(dtl.getClassNamePath());
            bidP.setNum(dtl.getQty());
            bidP.setMonthlyDifference(BigDecimal.valueOf(temporary.getPaymentWeek()));
            bidP.setSynthesizeTemporarySn(temporary.getSynthesizeTemporarySn());
            bidP.setState(null);
            bidP.setCreateOrgId(user.getEnterpriseId());
            bidP.setCreateOrgName(user.getEnterpriseName());
            bidP.setNetPrice(new BigDecimal(0));
            products.add(bidP);
            dtl.setIsBidding(1);
        }
        biddingProductService.saveBatch(products);
        synthesizeTemporaryDtlService.updateBatchById(synthesizeTemporaryDtls);
    }

    @Override
    public List<HitBidVo> getBiddingPurchaseBySynthesizeTemporarySn(String synthesizeTemporarySn) {
        List<HitBidVo> vos = super.baseMapper.selectByBiddingSn(synthesizeTemporarySn);
        return vos;
    }
}
