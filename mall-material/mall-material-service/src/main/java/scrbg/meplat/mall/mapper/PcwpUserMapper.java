package scrbg.meplat.mall.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import scrbg.meplat.mall.entity.PcwpUser;
import scrbg.meplat.mall.entity.pcwpmq.PcwpOrginfos;
import scrbg.meplat.mall.entity.pcwpmq.PcwpPersonPermissions;

import java.util.List;

@Mapper
@Repository
public interface PcwpUserMapper extends BaseMapper<PcwpUser> {
    List<PcwpUser> queryUserOrg(Page<PcwpUser> pages, @Param("ew")QueryWrapper<PcwpUser> wrapper);
    List<PcwpUser> queryUserByOrg(Page<PcwpUser> pages, @Param("ew")QueryWrapper<PcwpUser> wrapper);
    List<PcwpUser> queryUserInfoByOrg(@Param("ew")QueryWrapper<PcwpUser> wrapper);

    PcwpOrginfos selectUserCurrentOrgInfo(@Param("userNumber")String userNumber);


    List<PcwpOrginfos> getChildrenOrgList(@Param("userNumber")String userNumber);

}
