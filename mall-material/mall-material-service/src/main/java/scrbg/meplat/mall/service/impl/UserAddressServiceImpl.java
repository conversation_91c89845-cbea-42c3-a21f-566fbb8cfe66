package scrbg.meplat.mall.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import com.scrbg.common.utils.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.UserAddress;
import scrbg.meplat.mall.enums.user.AddressEnum;
import scrbg.meplat.mall.mapper.UserAddressMapper;
import scrbg.meplat.mall.service.UserAddressService;
import scrbg.meplat.mall.util.ThreadLocalUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @描述：用户地址 服务类
 * @作者: y
 * @日期: 2022-11-02
 */
@Service
public class UserAddressServiceImpl extends ServiceImpl<UserAddressMapper, UserAddress> implements UserAddressService {

    @Autowired
    private UserAddressService userAddressService;

    @Autowired
    MallConfig mallConfig;

    /**
     * 根据用户id查询所有收货地址并分页
     *
     * @param jsonObject
     * @param queryWrapper
     * @param userId
     * @return
     */
    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<UserAddress> queryWrapper, String userId) {
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        queryWrapper.eq(UserAddress::getUserId, userId);
        queryWrapper.eq(UserAddress::getMallType, mallConfig.mallType);
        queryWrapper.eq(UserAddress::getEnterpriseId,enterpriseId);
        queryWrapper.orderByDesc(UserAddress::getIsDefaultAddress);
        IPage<UserAddress> page = this.page(
                new Query<UserAddress>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }

    /**
     * Description:  根据userId进行新增
     */
    @Override
    public R create(UserAddress userAddress, LambdaQueryWrapper<UserAddress> queryWrapper) {
        //查询当前用户已填写的收货地址个数
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        queryWrapper.eq(UserAddress::getUserId, userAddress.getUserId()).
                eq(UserAddress::getEnterpriseId,enterpriseId).eq(UserAddress::getMallType, mallConfig.mallType);
        int count = userAddressService.count(queryWrapper);
        //第一个默认设置为默认地址
        if (count == 0) {
            userAddress.setIsDefaultAddress(AddressEnum.IS_DEFAULT_ADDRESS_YES.getCode());
            userAddress.setEnterpriseId(enterpriseId);
            userAddressService.save(userAddress);
            return R.success(null, "添加地址成功！");
        } else if (count <= 25) {
            userAddress.setIsDefaultAddress(AddressEnum.IS_DEFAULT_ADDRESS_NO.getCode());
            userAddressService.save(userAddress);
            return R.success(null, "添加地址成功！");
        } else {
            return R.failed("地址数量超过25个，请删除一些后再添加！");
        }

    }

    @Override
    public void update(UserAddress userAddress) {
        super.updateById(userAddress);
    }


    @Override
    public UserAddress getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        super.removeById(id);
    }


    /**
     * 设置默认地址
     *
     * @param userAddress
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R setDefaultAddress(UserAddress userAddress, LambdaQueryWrapper<UserAddress> queryWrapper) {
        Integer mallType = mallConfig.mallType;
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        //将该用户的地址全部设为非默认地址
        userAddress.setIsDefaultAddress(AddressEnum.IS_DEFAULT_ADDRESS_NO.getCode());
        queryWrapper.eq(UserAddress::getUserId, userAddress.getUserId());
        queryWrapper.eq(UserAddress::getEnterpriseId,enterpriseId);
        if (mallType != null) {
            queryWrapper.eq(UserAddress::getMallType, mallType);
        }
        userAddressService.update(userAddress, queryWrapper);
        //将根据地址id设为默认地址
        userAddress.setIsDefaultAddress(AddressEnum.IS_DEFAULT_ADDRESS_YES.getCode());
        queryWrapper.eq(UserAddress::getAddressId, userAddress.getAddressId());
        if (mallType != null) {
            queryWrapper.eq(UserAddress::getMallType, mallType);
        }
        userAddressService.update(userAddress, queryWrapper);
        return R.success("操作成功", "操作成功");
    }

    //查询默认地址
    @Override
    public UserAddress getDefaultAddress() {
        Integer defaultAddress = AddressEnum.IS_DEFAULT_ADDRESS_YES.getCode();
        Integer mallType = mallConfig.mallType;
        LambdaQueryChainWrapper<UserAddress> q = lambdaQuery();
        q.eq(UserAddress::getMallType, mallType);
        q.eq(UserAddress::getIsDefaultAddress, defaultAddress);
        UserAddress one = q.eq(UserAddress::getUserId, ThreadLocalUtil.getCurrentUser().getUserId())
                .eq(UserAddress::getEnterpriseId,ThreadLocalUtil.getCurrentUser().getEnterpriseId()).one();
        return one;
    }

    @Override
    public int isDefaultAddress() {
        LambdaQueryWrapper<UserAddress> queryWrapper = new LambdaQueryWrapper<>();
        String enterpriseId = ThreadLocalUtil.getCurrentUser().getEnterpriseId();
        String userId = ThreadLocalUtil.getCurrentUser().getUserId();
        queryWrapper.eq(UserAddress::getUserId, userId)
                .eq(UserAddress::getEnterpriseId,enterpriseId)
                .eq(UserAddress::getIsDefaultAddress,1)
                .eq(UserAddress::getMallType, mallConfig.mallType);
        return userAddressService.count(queryWrapper);
    }

    @Override
    public String getCityByAddress(String receiverAddress) {
        String city="";
        if (receiverAddress.contains("北京市") || receiverAddress.contains("上海市") || receiverAddress.contains("天津市") || receiverAddress.contains("重庆市")) {
            return city = receiverAddress.split("市")[0];
        } else {
            String regex="(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)(?<city>[^市]+自治州|.*?地区|.*?行政单位|.+盟|市辖区|.*?市|)";
            Matcher m=Pattern.compile(regex).matcher(receiverAddress);
            while(m.find()){
                return   city=m.group("city");
            }
            return city;
        }
    }
}
