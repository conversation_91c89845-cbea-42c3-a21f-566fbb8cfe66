package scrbg.meplat.mall.service.order;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

import scrbg.meplat.mall.dto.order.ProductBuyInfoDTO;
import scrbg.meplat.mall.entity.OrderItem;
import scrbg.meplat.mall.entity.Orders;
import scrbg.meplat.mall.entity.plan.Plan;

/**
 * 订单相关业务，OrdersService快1万行了，新逻辑在这里完成
 */
public interface OrderService extends IService<Orders> {
    /**
     * 下订单
     */
    void createMaterialOrder(List<ProductBuyInfoDTO> dtos, Plan plan);

    /**
     * 处理订单拆分（多供方订单）
     */
    void processOrderSplitting(Orders mainOrder, List<OrderItem> orderItems);

}
