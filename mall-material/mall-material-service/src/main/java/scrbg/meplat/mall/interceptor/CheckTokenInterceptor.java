package scrbg.meplat.mall.interceptor;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scrbg.common.utils.R;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.HandlerInterceptor;
import scrbg.meplat.mall.common.redis.RedisKey;
import scrbg.meplat.mall.common.wxApp.WxAppConfig;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.entity.PrivateKeySupplier;
import scrbg.meplat.mall.entity.ReceiptPerson;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.service.PrivateKeySupplierService;
import scrbg.meplat.mall.util.IpUtil;
import scrbg.meplat.mall.util.JwtTokenUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.UserLogin;
import scrbg.meplat.mall.vo.user.LoginVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2022-12-14 16:39
 */
@Log4j2
@Component
public class CheckTokenInterceptor implements HandlerInterceptor {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private PrivateKeySupplierService privateKeySupplierService;
    @Autowired
    private MallConfig mallConfig;
    @Autowired
    private JwtTokenUtil jwtUtil;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 拦截 api导入商品
     * 拦截登陆
     * 拦截收料小程序
     * 供应商小程序登陆
     * @param request current HTTP request
     * @param response current HTTP response
     * @param handler chosen handler to execute, for type and/or instance evaluation
     * @return
     * @throws Exception
     */

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 放行options请求
        String method = request.getMethod();
        if ("OPTIONS".equalsIgnoreCase(method)) {
            return true;
        }
        String token = request.getHeader("token");
        Integer mallType = null;
        String mallType1 = request.getHeader("mallType");
        mallType1 = String.valueOf(mallConfig.mallType);//这里原来是前端传输过来区分装备的，我直接强制赋值为商城。
        String requestURI = request.getRequestURI();
        if (StringUtils.isEmpty(mallType1)) {
            mallType = mallConfig.mallType;
        } else {
            Integer RQMallType = Integer.valueOf(mallType1);
            mallType = RQMallType;
        }
        if (requestURI.contains("/outer/open")) {//开放API给第三方厂家让供应商上架下架到商城
            String supplier_private_key = request.getHeader("supplier_private_key");
            String shop_id = request.getHeader("shop_id");
            if (StringUtils.isEmpty(supplier_private_key)) {
                throw new BusinessException(400, "未携带秘钥key！禁止访问");
            } else {
                if (StringUtils.isEmpty(shop_id)) {
                    throw new BusinessException(400, "未携带店铺id！");
                }
                PrivateKeySupplier privateKeySupplier = privateKeySupplierService.lambdaQuery()
                        .eq(PrivateKeySupplier::getShopId, shop_id)
                        .eq(PrivateKeySupplier::getPrivateKey, supplier_private_key)
                        .select(PrivateKeySupplier::getPrivateKey, PrivateKeySupplier::getInvalidTime, PrivateKeySupplier::getIpAuth).one();
                if (privateKeySupplier == null) {
                    throw new BusinessException(400, "供应商秘钥key错误！");
                } else {
                    if (privateKeySupplier.getInvalidTime() == null) {
                        throw new BusinessException(400, "秘钥key未配置过期时间！");
                    }
                    if (new Date().compareTo(privateKeySupplier.getInvalidTime()) == 1) {
                        throw new BusinessException(400, "该供应商秘钥key已过期！请联系管理员");
                    }
                    String ipAddress = request.getHeader("X-Forwarded-For").split(":")[0];
//                        String ipAddress = "************";
//                        String ipAddress = "**************";
                    if (privateKeySupplier.getIpAuth() == null) {
                        log.info("店铺id：" + shop_id, "ip地址：" + ipAddress);
                        throw new BusinessException(400, "未配置限制ip，请联系管理员！");
                    } else {
                        String ipAuth = privateKeySupplier.getIpAuth();
                        if(ipAuth.indexOf(ipAddress) != -1) {
                            return true;
                        }else {
                            return true;
                        }
                    }
                }
            }
        }

        if (token == null) {
            if (requestURI.contains("/oss/")) {
                throw new BusinessException(400, "请携带上传令牌！");
            }
            throw new BusinessException(401, "请先登陆");
        } else {
            if (requestURI.contains("/oss/")) {
                if (token.equals("UXp0UjJET2csMTY3MTA0MjIzMw.H83JNfMc8fze5OF-Vn4oSF4SqF4Wgm6PnZTK01adYvrQpLTZSN4ekA")) {
                    return true;
                } else {
                    throw new BusinessException(400, "请传入正确的上传令牌");
                }
            }
            // 收料微信app登录
            if (token.startsWith(WxAppConfig.wxAppPrefixSL)) {
                String tokenStr = token.substring(WxAppConfig.wxAppPrefixSL.length());
                String phone = jwtUtil.getUserNameFromToken(tokenStr);
                if (stringRedisTemplate.hasKey(RedisKey.WX_APP_SL_USER_MAP_KEY + mallType + "_" + phone)) {
                    UserLogin user = new UserLogin();
                    ReceiptPerson person = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.WX_APP_SL_USER_MAP_KEY + mallType + "_" + phone), ReceiptPerson.class);
                    user.setOrgId(person.getLocalOrgId());
                    user.setEnterpriseId(person.getLocalOrgId());
                    user.setEnterpriseName(person.getLocalOrgName());
                    user.setUserName(person.getName());
                    user.setOriginalUserName(person.getName());
                    ThreadLocalUtil.addCurrentUser(user);
                    stringRedisTemplate.expire(RedisKey.WX_APP_SL_USER_MAP_KEY + mallType + "_" + phone, mallConfig.loginOutTime, TimeUnit.MINUTES);
                    return true;
                } else {
                    throw new BusinessException(401, "登录过期，请重新登陆");
                }
            }

            UserLogin user = new UserLogin();
            String url = mallConfig.prodPcwp2Url + "/identity/auth/verifyToken?token=" + token;//每一步操作都去验证用户的合法性
            HttpHeaders thisHeaders = new HttpHeaders();
            thisHeaders.add("sysCode", "msp");
            HashMap<String, Object> thisMap = new HashMap<>();
            thisMap.put("sysCode", "msp");
            String content = JSON.toJSONString(thisMap);
            HttpEntity<String> thisRequest = new HttpEntity<>(content, thisHeaders);
            long thisStartTime = System.currentTimeMillis();
            R<Map> r = null;
            try {
                r = restTemplate.postForObject(url, thisRequest, R.class);
            } catch (Exception e) {
                log.info(e.getMessage());
                throw new BusinessException("【远程异常】远程检验认证错误！"+e.getMessage());
//                Map map = new HashMap();
//                map.put("userId","b03c1eef3204-b9e7-e74c-b8b6-281daaad");
//                r = R.success(map,"");
            }
            long thisEndTime = System.currentTimeMillis();
            long newDateTime = thisEndTime - thisStartTime;
            log.info("调用pcwp校验token耗时：" + newDateTime);
            if (r.getCode() == 200) {
                Map dataMap = r.getData();
                String id = (String) dataMap.get("userId");
                if (stringRedisTemplate.hasKey(RedisKey.USER_MAP_KEY + mallType + "_" + id)) {
                    LoginVO vo = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.USER_MAP_KEY + mallType + "_" + id), LoginVO.class);
//                    System.out.println("redis用户信息：" + vo);
                    if (vo.getIsInterior() == 1 && !requestURI.contains("userCenter/user/cutOrg")) {
                        String org = request.getHeader("org");
                        if (StringUtils.isEmpty(org)) {
                            throw new BusinessException("未携带机构信息！");
                        }
                        String decodedString = URLDecoder.decode(org, StandardCharsets.UTF_8.toString());
                        Map map = JSON.parseObject(decodedString, Map.class);
                        String orgId = (String) map.get("orgId");
                        if (!orgId.equals(vo.getOrgId())) {
                            throw new BusinessException(500485, "检查到当前用户机构被切换！重新登陆或重新切换当前机构！");
                        }
                    }
                    user.setUserId(vo.getUserId());
                    user.setShopId(vo.getShopId());
                    user.setShopName(vo.getShopName());
                    user.setOrgId(vo.getOrgId());
                    user.setEnterpriseId(vo.getLocalOrgId());
                    user.setFarUserId(vo.getFarUserId());
                    user.setOrgIds(vo.getOrgIds());
                    user.setUserMobile(vo.getUserMobile());
                    user.setToken(vo.getToken());
                    user.setIsInterior(vo.getIsInterior());
                    user.setEnterpriseName(vo.getEnterpriseName());
                    user.setUserName(vo.getUserName());
                    user.setOrgInfo(vo.getOrgInfo());
                    user.setIsSupplier(vo.getIsSupplier());
                    user.setEnterpriseType(vo.getEnterpriseType());
                    user.setSocialCreditCode(vo.getSocialCreditCode());
                    user.setIsCheck(vo.getIsCheck());
                    user.setUserNumber(vo.getUserNumber());
                    user.setOriginalUserName(vo.getOriginalUserName());
                    user.setIsSubmitOrder(vo.getIsSubmitOrder());
                    user.setIsMonthPlanAudit(vo.getIsMonthPlanAudit());
                    user.setIsPlatformAdmin(vo.getIsPlatformAdmin());
                    user.setRoles(vo.getRoles());
                    user.setOrgAndSon(vo.getOrgAndSon());
                    user.setMallRoles(vo.getMallRoles());
                    user.setMapSysMenu(vo.getMapSysMenu());
                    user.setPermissions(vo.getPermissions());
                    user.setMallRoleList(vo.getMallRoleList());
                    ThreadLocalUtil.addCurrentUser(user);
                    stringRedisTemplate.expire(RedisKey.USER_MAP_KEY + mallType + "_" + id, mallConfig.loginOutTime, TimeUnit.MINUTES);
                    return true;
                } else {
                    throw new BusinessException(401, "登录过期，请重新登陆");
                }
            }
            //后面的代码好像根本执行不到，后续可能要删除掉
            // 供应商登录校验
            if (token.startsWith(WxAppConfig.wxAppPrefixSupplier)) {
                String tokenStr = token.substring(WxAppConfig.wxAppPrefixSupplier.length());
                String phone = jwtUtil.getUserNameFromToken(tokenStr);
                if (stringRedisTemplate.hasKey(RedisKey.WX_APP_SUPPLIER_USER_MAP_KEY + mallType + "_" + phone)) {
                    LoginVO vo = JSON.parseObject(stringRedisTemplate.opsForValue().get(RedisKey.WX_APP_SUPPLIER_USER_MAP_KEY + mallType + "_" + phone), LoginVO.class);
                    UserLogin userLogin = new UserLogin();
                    userLogin.setUserId(vo.getUserId());
                    userLogin.setShopId(vo.getShopId());
                    userLogin.setShopName(vo.getShopName());
                    userLogin.setOrgId(vo.getOrgId());
                    userLogin.setEnterpriseId(vo.getLocalOrgId());
                    userLogin.setFarUserId(vo.getFarUserId());
                    userLogin.setOrgIds(vo.getOrgIds());
                    userLogin.setUserMobile(vo.getUserMobile());
                    userLogin.setToken(vo.getToken());
                    userLogin.setIsInterior(vo.getIsInterior());
                    userLogin.setEnterpriseName(vo.getEnterpriseName());
                    userLogin.setUserName(vo.getUserName());
                    userLogin.setOrgInfo(vo.getOrgInfo());
                    userLogin.setIsSupplier(vo.getIsSupplier());
                    userLogin.setEnterpriseType(vo.getEnterpriseType());
                    userLogin.setSocialCreditCode(vo.getSocialCreditCode());
                    userLogin.setIsCheck(vo.getIsCheck());
                    userLogin.setUserNumber(vo.getUserNumber());
                    userLogin.setOriginalUserName(vo.getOriginalUserName());
                    userLogin.setIsSubmitOrder(vo.getIsSubmitOrder());
                    userLogin.setIsMonthPlanAudit(vo.getIsMonthPlanAudit());
                    userLogin.setIsPlatformAdmin(vo.getIsPlatformAdmin());
                    userLogin.setRoles(vo.getRoles());
                    userLogin.setOrgAndSon(vo.getOrgAndSon());
                    userLogin.setMallRoles(vo.getMallRoles());
                    userLogin.setMapSysMenu(vo.getMapSysMenu());
                    ThreadLocalUtil.addCurrentUser(userLogin);
                    stringRedisTemplate.expire(RedisKey.WX_APP_SUPPLIER_USER_MAP_KEY + mallType + "_" + phone, mallConfig.loginOutTime, TimeUnit.MINUTES);
                    return true;
                } else {
                    throw new BusinessException(401, "登录过期，请重新登陆");
                }
            } else {
                throw new BusinessException(r.getCode(), r.getMessage());
            }
        }
    }

    private void doResponse(HttpServletResponse response, R r) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        PrintWriter out = response.getWriter();
        String s = new ObjectMapper().writeValueAsString(r);
        out.print(s);
        out.flush();
        out.close();
    }

    /**
     * 接口访问结束后，从ThreadLocal中删除用户信息
     *
     * @param response
     * @param handler
     * @param ex
     * @throws Exception
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception
            ex) throws Exception {
        ThreadLocalUtil.remove();
    }
}
