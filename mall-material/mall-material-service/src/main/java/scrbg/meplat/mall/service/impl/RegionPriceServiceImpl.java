package scrbg.meplat.mall.service.impl;

import java.math.BigDecimal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.RegionPrice;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.RegionPriceMapper;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.RegionPriceService;

@Service
public class RegionPriceServiceImpl extends ServiceImpl<RegionPriceMapper, RegionPrice> implements RegionPriceService {
    @Autowired
    private ProductService productService;

    @Override
    public BigDecimal getCostPrice(String regionPriceId) {
        RegionPrice regionPrice = this.getById(regionPriceId);
        if (regionPrice == null) {
            throw new BusinessException("商品区域价格不存在");
        }
        Product product = productService.getById(regionPrice.getProductId());
        // 这个成本价没有用，一级供应商相关的订单等不提供利润计算功能
        if (product.getProductSource() == 1) {
            return regionPrice.getTaxInPrice();
        }
        if (product.getProductSource() == 2) {
            return regionPrice.getTaxInPrice();
        }
        if (product.getProductSource() == 3) {
            return product.getPurchasePrice();
        }
        throw new BusinessException("商品来源异常");
    }

    @Override
    public BigDecimal getSellPrice(String regionPriceId) {
        RegionPrice regionPrice = this.getById(regionPriceId);
        if (regionPrice == null) {
            throw new BusinessException("商品区域价格不存在");
        }
        Product product = productService.getById(regionPrice.getProductId());
        if (product.getProductSource() == 1) {
            return regionPrice.getTaxInPrice();
        }
        if (product.getProductSource() == 2) {
            return regionPrice.getBonusTaxInPrice();
        }
        if (product.getProductSource() == 3) {
            return regionPrice.getTaxInPrice();
        }
        throw new BusinessException("商品来源异常");
    }
}
