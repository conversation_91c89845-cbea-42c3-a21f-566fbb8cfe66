package scrbg.meplat.mall.service.system;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.dto.DsaffDTO;
import scrbg.meplat.mall.dto.OrganizationDTO;
import scrbg.meplat.mall.dto.outer.login.LoginUser;
import scrbg.meplat.mall.dto.outer.login.ResponseBody;
import scrbg.meplat.mall.dto.outer.login.UserInfo;
import scrbg.meplat.mall.dto.system.UserPasswordDTO;
import scrbg.meplat.mall.dto.user.userCenter.CreateShopExternalDTO;
import scrbg.meplat.mall.dto.user.userCenter.CreateShopInsideDTO;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.User;
import scrbg.meplat.mall.entity.system.SysRole2;
import scrbg.meplat.mall.entity.system.SysUser;
import scrbg.meplat.mall.vo.user.LoginVO;
import scrbg.meplat.mall.vo.user.UserShopEnterpriseInfoVO;
import scrbg.meplat.mall.vo.user.userCenter.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

public interface SysUserService extends IService<SysUser> {


    PageUtils<SysUser> queryPage(JSONObject jsonObject, LambdaQueryWrapper<User> queryWrapper);
    PageUtils<SysUser> queryInnerPage(JSONObject jsonObject, LambdaQueryWrapper<SysUser> queryWrapper);

    void createUser(SysUser user);

    //同步用户

    void updateUser(SysUser user);

    void updateUserState(SysUser user);

    SysUser getUserById(String id);

    void deleteUser(String id);

    void resetPassword(UserPasswordDTO passwordDTO);

    List<SysRole2> getRoleList();


    SysUser loginByAccount(SysUser user);

    R loginByMobile(SysUser user);

    /**
     * 个人用户注册（外部）
     * @param SysUser
     * @return
     */
    R registration(SysUser user);


    Boolean verifyUserInfo();

    void updateByPublish(List<String> ids, String s);

    /**
     * 用户登陆接口
     *
     * @param account
     * @param password
     * @param mallType
     * @param request
     */
    LoginVO login(String account, String password, Integer mallType, HttpServletRequest request);

    /**
     * 开店回显数据
     * @return
     */
    CreateShopEchoInfoVO createShopEchoInfo(Integer mallType);

    /**
     * 开店 外部
     * @param dto
     */
    void createShopExternal(CreateShopExternalDTO dto);

    /**
     * 根据当前的登陆的用户id查询店铺状态
     * @return
     */
    ShopStateVO getShopStateByUserId();

    /**
     * 开店回显数据（内部）
     * @return
     */
    CreateInsideShopEchoInfoVO creatInsideShopEchoInfo();


    /**
     * 个人开店（内部）
     * @param dto
     */
    void createShopInside(CreateShopInsideDTO dto);



    /**
     * 手机号登陆
     *
     * @param phone
     * @param code
     * @param mallType
     * @param request
     * @return
     */
    LoginVO phoneLogin(String phone, String code, Integer mallType, HttpServletRequest request);

    /**
     * 登陆发送手机验证码
     *
     * @param phone
     * @param privateKeyId
     * @param request
     */
    void loginSendCode(String phone, String privateKeyId, HttpServletRequest request);

    /**
     * 注册发送手机验证码
     *
     * @param phone
     * @param privateKeyId
     */
    void registerSendCode(String phone, String privateKeyId);

    /**
     * 根据当前的登陆的用户id获取企业信息
     * @return
     */
    EnterpriseAuthUpdateVO getEnterpriseAuthInfo(Integer mallType);


    SysUser getUserData();

    void test();
    void test2();

    /**
     * 根据 用户id修改用户头像信息
     * @param userId 用户id
     * @param userImg 用户头像
     */
    void updateUserImg(String userId, String userImg);

    /**
     * 获取用户的信息企业信息店铺信息
     * @return
     */
    UserShopEnterpriseInfoVO getUserShopEnterpriseInfo();

    /**
     * 用户退出登陆
     */
    void loginOut();

    /**
     * tt登陆
     *
     * @param a
     * @param p
     * @param mallType
     * @param request
     * @return
     */
    LoginVO ttLogin(String a, String p, Integer mallType, HttpServletRequest request);


    /**
     * 切换企业
     *
     * @param dto
     * @param request
     * @return
     */
    LoginVO cutOrg(OrganizationDTO dto, HttpServletRequest request);

    /**
     * token登陆
     *
     * @param token
     * @param mallType
     * @param request
     * @return
     */
    LoginVO tokenLogin(String token, Integer mallType, HttpServletRequest request);

    /**
     * 获取当前登陆用户名称和头像
     * @return
     */
    UserNameAndImgVO getUserNameInfo();

    /**
     * 修改密码发送验证码
     *
     * @param phone
     * @param privateKeyId
     * @param request
     */
    void updatePassSendCode(String phone, String privateKeyId, HttpServletRequest request);

    /**
     * 未登录时修改账号密码
     * @param phone
     * @param privateKeyId
     * @param request
     */

    void noUpdatePassSendCode(String phone, String privateKeyId, HttpServletRequest request);

    /**
     * 检验修改密码的验证码
     * @param phone
     * @param code
     * @return
     */
    void checkUpdatePassCode(String phone, String code);

    /**
     * 修改密码
     * @param newPassword
     */
    void updatePassword(String newPassword);


    /**
     * 修改密码
     * @param newPassword
     */
    void updatePassword(String userPhone,String newPassword);

    /**
     * 修改手机号发送验证码
     *
     * @param phone
     * @param privateKeyId
     * @param request
     */
    void updatePhoneSendCode(String phone, String privateKeyId, HttpServletRequest request);

    /**
     * 检验修改手机的验证码
     * @param phone
     * @param code
     */
    void checkUpdatePhoneCode(String phone, String code);

    /**
     * 修改手机号
     * @param newPhone
     * @param code
     */
    void updatePhone(String newPhone, String code);


    /**
     * 查询用户分页统计列表
     * @param jsonObject
     * @param objectLambdaQueryWrapper
     * @return
     */
    PageUtils getUserCountList(JSONObject jsonObject, LambdaQueryWrapper<SysUser> objectLambdaQueryWrapper);

    /**
     * 根据账号获取用户的信息企业信息店铺信息
     *
     * @param userNumber
     * @param mallType
     * @return
     */
    UserShopEnterpriseInfoVO getUserShopEnterpriseInfoByUserSn(String userNumber, Integer mallType);

    /**
     * 供应商登陆
     *
     * @param account
     * @param password
     * @param mallType
     * @param request
     * @return
     */
    LoginVO supplierLogin(String account, String password, Integer mallType, HttpServletRequest request);

    /**
     * 供应商手机号登陆
     *
     * @param phone
     * @param code
     * @param mallType
     * @param request
     * @return
     */
    LoginVO supplierPhoneLogin(String phone, String code, Integer mallType, HttpServletRequest request);

    /**
     * 根据信用代码查询是否是供应商
     *
     * @param socialCreditCode
     * @param mallType
     * @return
     */
    boolean getIsSupplier(String socialCreditCode, Integer mallType);


    /**
     * 批量修改状态
     *
     * @param jsonObject
     * @param request
     */
    void updateBatchUserState(JSONObject jsonObject, HttpServletRequest request);

    List<SysUser> getUserListByids(List<String> ids);

    EnterpriseInfo getSupplierInfo(String socialCreditCode, Integer mallType);

    EnterpriseInfo getAuditState();

    /**
     * 内部用户登录接口
     *
     * @param account
     * @param password
     * @param mallType
     * @param request
     * @return
     */
    LoginVO interiorLogin(String account, String password, Integer mallType, HttpServletRequest request);

    void createlasjldf(DsaffDTO dto);

    /**
     * 首次登录处理
     * @param vo
     * @return
     */
    public LoginVO handleFirstLogin(LoginVO vo);

    /**
     * 根据用户手机号查询当前密码错误次数和状态
     * @param phone
     */
    Map getLockingByPhone (String phone);

    /**
     * 密码错误处理错误次数
     * @param phone
     */
    void handlePwdError(String phone);

    /**
     * 上次修改密码时间是否超过三十天
     * @param phone
     */
    void handlePwdExpire(String phone);

    // 临期提醒/**
    /**
     *
     * @param phone
     */
    Map handleExpireing(String phone);
    /**
     * 登录成功之后，密码错误次数重置
     * @param phone
     */
    void handleuccessLogin(String phone);

    /**
     * 三方登录获取token
     * @param user
     * @return
     */
    ResponseBody thirdLogin(LoginUser user, HttpServletRequest request);

    /**
     * 通过token返回用户基础信息
     * @return 用户基础信息
     */
    UserInfo verifyToken();


    /**
     * 添加菜单
     * @param vo
     */
    void addUserMenu(LoginVO vo);
}
