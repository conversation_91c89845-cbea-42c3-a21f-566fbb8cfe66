package scrbg.meplat.mall.service.stockManage.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import scrbg.meplat.mall.common.constant.ProcessConstants;
import scrbg.meplat.mall.config.MallConfig;
import scrbg.meplat.mall.controller.stockManage.vo.InboundSettlementVO;
import scrbg.meplat.mall.entity.InboundSettlementManage;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.stockManage.InboundSettlementManageMapper;
import scrbg.meplat.mall.service.ProcessConfigService;
import scrbg.meplat.mall.service.stockManage.InboundSettlementService;
import scrbg.meplat.mall.util.DateUtil;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.util.countExcel.ExcelForWebUtil;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class InboundSettlementServiceImpl extends ServiceImpl<InboundSettlementManageMapper, InboundSettlementManage> implements InboundSettlementService {


    public static final String DATEFORMAT = "yyyyMM";
    private InboundSettlementManageMapper inboundSettlementManageMapper;

    private MallConfig mallConfig;

    private ProcessConfigService processConfigService;

    @Autowired
    public void setInboundSettlementManageMapper(InboundSettlementManageMapper inboundSettlementManageMapper) {
        this.inboundSettlementManageMapper = inboundSettlementManageMapper;
    }

    @Autowired
    public void setMallConfig(MallConfig mallConfig) {
        this.mallConfig = mallConfig;
    }

    @Autowired
    public void setProcessConfigService(ProcessConfigService processConfigService) {
        this.processConfigService = processConfigService;
    }

    @Override
    public void saveSettlement(InboundSettlementManage inboundSettlementManage) {
        inboundSettlementManage.setAuditStatus(0);
        inboundSettlementManage.setWarehouseId("1");
        inboundSettlementManage.setAccountPeriod(generateAccountPeriod());
        inboundSettlementManage.setReceiveName(ThreadLocalUtil.getCurrentUser().getUserName());
        inboundSettlementManage.setReceivePhone(ThreadLocalUtil.getCurrentUser().getUserMobile());
        inboundSettlementManage.setGmtCreate(new Date());
        save(inboundSettlementManage);
    }

    @Override
    public void saveAndSubmitSettlement(InboundSettlementManage inboundSettlementManage) {
        inboundSettlementManage.setAuditStatus(1);
        inboundSettlementManage.setStoredWarehouseTime(
                null == inboundSettlementManage.getStoredWarehouseTime() ?
                        new Date() :
                        inboundSettlementManage.getStoredWarehouseTime()
        );
        inboundSettlementManage.setAccountPeriod(generateAccountPeriod());
        inboundSettlementManage.setWarehouseId("1");
        inboundSettlementManage.setReceiveName(ThreadLocalUtil.getCurrentUser().getUserName());
        inboundSettlementManage.setReceivePhone(ThreadLocalUtil.getCurrentUser().getUserMobile());
        inboundSettlementManage.setGmtCreate(new Date());
        save(inboundSettlementManage);
        processConfigService.myFunc(ProcessConstants.INBOUND_SETTLEMENT,
                ThreadLocalUtil.getCurrentUser(), 0, inboundSettlementManage.getId(), "");
    }

    @Override
    public void updateAndSubmitSettlement(InboundSettlementManage inboundSettlementManage) {
        updateById(inboundSettlementManage);
        processConfigService.myFunc(ProcessConstants.INBOUND_SETTLEMENT,
                ThreadLocalUtil.getCurrentUser(), 0, inboundSettlementManage.getId(), "");
    }

    @Override
    public void updateSettlement(InboundSettlementManage inboundSettlementManage) {
        updateById(inboundSettlementManage);
    }

    @Override
    public PageUtils<InboundSettlementManage> queryPage(JSONObject jsonObject, LambdaQueryWrapper<InboundSettlementManage> queryWrapper) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String keywords = (String) innerMap.get("keywords");
        String contractNo = (String) innerMap.get("contractNo");
        String supplierName = (String) innerMap.get("supplierName");
        Integer inboundType = (Integer) innerMap.get("inboundType");
        Integer auditStatus = (Integer) innerMap.get("auditStatus");
        String startCreateDate = (String) innerMap.get("startCreateDate");
        String endCreateDate = (String) innerMap.get("endCreateDate");
        if (StringUtils.isNotBlank(keywords)) {
            queryWrapper.and((t) -> {
                t.like(InboundSettlementManage::getContractNo, keywords)
                        .or()
                        .like(InboundSettlementManage::getSupplierName, keywords);

            });
        }
        if (!StringUtils.isEmpty(contractNo)) {
            queryWrapper.like(InboundSettlementManage::getContractNo, contractNo);
        }
        if (null != inboundType) {
            queryWrapper.eq(InboundSettlementManage::getInboundType, inboundType);
        }
        if (null != auditStatus) {
            queryWrapper.eq(InboundSettlementManage::getAuditStatus, auditStatus);
        }
        if (!StringUtils.isEmpty(supplierName)) {
            queryWrapper.like(InboundSettlementManage::getSupplierName, supplierName);
        }
        queryWrapper.between(StringUtils.isNotBlank(startCreateDate) && StringUtils.isNotEmpty(endCreateDate), InboundSettlementManage::getGmtCreate, startCreateDate, endCreateDate);
        IPage<InboundSettlementManage> page = this.page(
                new Query<InboundSettlementManage>().getPage(jsonObject),
                queryWrapper
        );
        page.getRecords().forEach(item -> {
            item.setAccountPeriod(convertPeriod(item.getAccountPeriod()));
        });
        return new PageUtils<InboundSettlementManage>(page);
    }

    @Override
    public void export(String reconciliationId, HttpServletResponse response) {
        InboundSettlementManage manage = baseMapper.selectById(reconciliationId);
        try {
            String src = mallConfig.templateFormUrl;
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("shipData", DateUtil.getyyymmddHHmmss(new Date()));
            dataMap.put("receiveOrgName", manage.getReceiveOrgName());
            dataMap.put("supplierName", manage.getSupplierName());
            dataMap.put("gmtCreate", DateUtil.getyyymmddHHmmss(manage.getGmtCreate()));
            dataMap.put("invoiceNum", manage.getInvoiceNum());
            dataMap.put("accountPeriod", convertPeriod(manage.getAccountPeriod()));
            dataMap.put("contractNo", manage.getContractNo());
            dataMap.put("desc", manage.getRemark());
            String settlementInfo = manage.getSettlementInfo();
            if (StringUtils.isNotBlank(settlementInfo)) {
                List<InboundSettlementVO> list = JSON.parseArray(settlementInfo, InboundSettlementVO.class);
                dataMap.put("sumQuantity", list.stream()
                        .map(InboundSettlementVO::getQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                dataMap.put("sumTotalAmount", list.stream()
                        .map(InboundSettlementVO::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                dataMap.put("sumNoRateAmount", list.stream()
                        .map(InboundSettlementVO::getNoRateAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                dataMap.put("dataList", list);
            }
            ExcelForWebUtil.exportExcel(response, dataMap, "物资入库结算单模板.xlsx", src, "物资入库结算单.xlsx");
        } catch (Exception e) {
            log.error("导出失败信息：" + e.getMessage());
            throw new BusinessException(500, "导出失败！");
        }
    }

    private synchronized String generateAccountPeriod() {
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DATEFORMAT));
        AtomicInteger finalSerialNum = new AtomicInteger(1);
        Integer serialNumber = inboundSettlementManageMapper.selectMaxSerialNumber(currentDate, 1);
        if (null == serialNumber) {
            inboundSettlementManageMapper.insertSerial(currentDate,
                    finalSerialNum.get(), 1);
        } else {
            finalSerialNum.set(new AtomicInteger(serialNumber).incrementAndGet());
            inboundSettlementManageMapper.updateSerial(currentDate,
                    finalSerialNum.get(), 1);
        }
        return currentDate + finalSerialNum.get();
    }

    public static String convertPeriod(String period) {
        if (StringUtils.isBlank(period)) {
            return "";
        }
        // 使用正则表达式匹配格式
        if (period.matches("(\\d{4})(\\d{2})(\\d+)")) {
            String yearMonth = period.substring(0, 6);
            String issue = period.substring(6);

            return yearMonth + " 月 第" + issue + "期";
        }

        return "";
    }


    @Override
    public void updateState(String id, int state) {
        if(processConfigService.processIsFinished(id) || state == 3){
            InboundSettlementManage manage = new InboundSettlementManage();
            manage.setId(id);
            manage.setAuditStatus(state);
            updateById(manage);
        }
        processConfigService.myFunc(ProcessConstants.INBOUND_SETTLEMENT,
                ThreadLocalUtil.getCurrentUser(), state - 1, id,
                1 == state ? "【提交】":  2 == state ? "【同意】": "【驳回】");
    }

    @Override
    public void submitSettlement(String id) {
      this.updateState(id, 1);
    }
}
