package scrbg.meplat.mall.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.DynamicParameter;
import com.github.xiaoymin.knife4j.annotations.DynamicParameters;
import com.scrbg.common.utils.PageR;
import com.scrbg.common.utils.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import scrbg.meplat.mall.entity.PcwpOrg;
import scrbg.meplat.mall.service.PcwpOrgService;
import com.scrbg.common.utils.R;
import scrbg.meplat.mall.service.PcwpUserService;

import java.util.List;


@RestController
@RequestMapping("/pcwpOrg")
@Api(tags = "pcwp机构表内部人员表")
public class PcwpOrgController {
    @Autowired
    public PcwpOrgService pcwpOrgService;
    @Autowired
    public PcwpUserService pcwpUserService;
    @GetMapping("/getTree")
    @ApiOperation(value = "获取pcwp机构树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "机构名称",
                    dataType = "String", paramType = "query"),
    })
    public R<List<PcwpOrg>> getTree(String name) {
        return R.success(pcwpOrgService.getTree(name));
    }

    @GetMapping("/getTree4User")
    @ApiOperation(value = "获取pcwp机构树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "机构名称",
                    dataType = "String", paramType = "query"),
    })
    public R<List<PcwpOrg>> getTree4User(String id,String name) {
        return R.success(pcwpOrgService.getTree4User(id,name));
    }
    @PostMapping("/getInUserLedger")
    @ApiOperation(value = "获取内部采购员台账")
    @DynamicParameters(name = "内部采购员分页查询", properties = {
            @DynamicParameter(name = "page", value = "当前页数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "limit", value = "每页显示条数", dataTypeClass = Integer.class),
            @DynamicParameter(name = "orgName", value = "所属机构", dataTypeClass = String.class),
            @DynamicParameter(name = "shortCodes", value = "机构简码", dataTypeClass = List.class),
            @DynamicParameter(name = "keywords", value = "关键字", dataTypeClass = String.class),
    })
    public PageR getInUserLedger(@RequestBody JSONObject jsonObject) {
        PageUtils page = pcwpUserService.getInUserLedger(jsonObject);
        return PageR.success(page);
    }
}
