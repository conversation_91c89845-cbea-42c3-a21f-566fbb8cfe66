package scrbg.meplat.mall.component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import lombok.RequiredArgsConstructor;
import scrbg.meplat.mall.entity.EnterpriseInfo;
import scrbg.meplat.mall.entity.Product;
import scrbg.meplat.mall.entity.Shop;
import scrbg.meplat.mall.entity.plan.Plan;
import scrbg.meplat.mall.entity.plan.PlanDetail;
import scrbg.meplat.mall.enums.product.ProductEnum;
import scrbg.meplat.mall.service.EnterpriseInfoService;
import scrbg.meplat.mall.service.ProductService;
import scrbg.meplat.mall.service.ProductSkuService;
import scrbg.meplat.mall.service.ShopService;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.vo.product.website.SubmitOrderByPlanVO;
import scrbg.meplat.mall.vo.product.website.SubmitOrderProductItemInfoByPlanVO;

/**
 * 校验购物车数据并生成购物车详情
 */
@Component
@RequiredArgsConstructor
public class OrderDetailChecker {

    private final EnterpriseInfoService enterpriseInfoService;
    private final ShopService shopService;
    private final ProductService productService;
    private final ProductSkuService productSkuService;

    public List<SubmitOrderByPlanVO> checkPlanDetails(Plan plan) {

        List<PlanDetail> details = plan.getDetails();

        List<SubmitOrderByPlanVO> buyVOS = buildSubmitOrderVOList(details, plan);
        // TODO 原来的代码使用只处理了数组当中的第一个
        for (SubmitOrderByPlanVO submitOrderByPlanVO : buyVOS) {
            BigDecimal total = BigDecimal.ZERO;
            for (SubmitOrderProductItemInfoByPlanVO p : submitOrderByPlanVO.getProductInfo()) {
                total = total.add(p.getNumTotalPrice());
            }
            submitOrderByPlanVO.setTotalPrice(total);
            
        }
        return buyVOS;
    }

    private EnterpriseInfo resolveEnterpriseInfo(PlanDetail detail) {
        String creditCode = detail.getCreditCode();
        if (StringUtils.isEmpty(creditCode)) {
            EnterpriseInfo info = enterpriseInfoService.lambdaQuery()
                    .eq(EnterpriseInfo::getInteriorId, detail.getStorageOrgId())
                    .select(EnterpriseInfo::getEnterpriseId, EnterpriseInfo::getShortCode).one();
            detail.setOrgShort(info.getShortCode());
            return info;
        } else {
            EnterpriseInfo info = enterpriseInfoService.lambdaQuery()
                    .eq(EnterpriseInfo::getSocialCreditCode, creditCode)
                    .select(EnterpriseInfo::getEnterpriseId).one();
            return info;
        }
    }

    private List<SubmitOrderByPlanVO> buildSubmitOrderVOList(List<PlanDetail> details, Plan plan) {
        Map<String, List<PlanDetail>> grouped = new HashMap<String, List<PlanDetail>>();
        for (PlanDetail d : details) {
            String key = d.getShopId();
            if (!grouped.containsKey(key)) {
                grouped.put(key, new ArrayList<PlanDetail>());
            }
            grouped.get(key).add(d);
        }

        List<SubmitOrderByPlanVO> vos = new ArrayList<SubmitOrderByPlanVO>();

        for (Map.Entry<String, List<PlanDetail>> entry : grouped.entrySet()) {
            String shopId = entry.getKey();
            String shopName = shopService.getShopNameById(shopId);

            SubmitOrderByPlanVO vo = new SubmitOrderByPlanVO();
            vo.setShopId(shopId);
            vo.setShopName(shopName);

            List<SubmitOrderProductItemInfoByPlanVO> productInfos = new ArrayList<SubmitOrderProductItemInfoByPlanVO>();
            boolean allChecked = true;

            for (PlanDetail item : entry.getValue()) {
                BigDecimal taxRate = plan.getTaxRate();
                // 保存product信息，sku信息，和resCode（返回码，200正常，50010异常，50000库存不够）和 cause（异常原因描述）;
                SubmitOrderProductItemInfoByPlanVO productInfo = new SubmitOrderProductItemInfoByPlanVO();
                BeanUtils.copyProperties(productService.getProductById(item.getTradeId(), ProductEnum.STATE_PUTAWAY.getCode()), productInfo);
                BeanUtils.copyProperties(productSkuService.getProductSkuByProductId(item.getTradeId(), null).get(0), productInfo);

                fillProductInfoFields(productInfo, item, shopName, plan, taxRate);
                Integer notConsumeNumber = item.getNotConsumeNumber();
                Integer consumeNumber = item.getQuantity() - notConsumeNumber;

                productInfos.add(productInfo);

                // 下边就是判断数据是否合法，并填充 resCode（返回码，200正常，50010异常，50000库存不够）和 cause（异常原因描述）
                // 还有给shopId赋值
                // 购买数量大于现有库存有特殊处理
                if (item.getQuantity().equals(consumeNumber)) {
                    markError(productInfo, "计划已完成！");
                    continue;
                }
                EnterpriseInfo enterpriseInfo = resolveEnterpriseInfo(item);
                if (enterpriseInfo == null) {
                    markError(productInfo, "供应商未注册！");
                    continue;
                };

                // 这里可以根据商品id找到商品，商品里有店铺id，暂时沿用老代码的思路
                Shop shop = shopService.lambdaQuery().eq(Shop::getEnterpriseId, enterpriseInfo.getEnterpriseId())
                        .select(Shop::getShopId, Shop::getState).one();
                if (shop == null) {
                    markError(productInfo, "店铺不存在！");
                    continue;
                }
                if (shop.getState()==0) {
                    // 新增50020 状态，只有店铺冻结的情况下该商品不允许下单
                    productInfo.setCause("店铺已冻结！");
                    productInfo.setResCode(50020);
                    continue;
                }
                item.setShopId(shop.getShopId());
                productInfo.setShopId(shop.getShopId());

                String productId = item.getTradeId();
                Product product = productService.getProductById(productId, null);
                if (product == null) {
                    markError(productInfo, "商品不存在！");
                    continue;
                }
                // 商品下架也可以购买
                // else if (product.getState()!=ProductEnum.STATE_PUTAWAY.getCode()) {
                //     markError(productInfo, "商品已下架！");
                //     continue;
                    
                // }
                // 库存在生成计划的时候已经扣除，这里不用考虑
                // BigDecimal stock = productInfo.getStock();
                // if (new BigDecimal(notConsumeNumber).compareTo(stock) > 0) {
                //     productInfo.setCause("【警告】购买数量大于现有库存！");
                //     productInfo.setResCode(50000);
                //     productInfo.setCartNum(stock);
                //     productInfo.setCartMaxNum(stock);
                //     // 注意这里使用计划里存储的单价
                //     productInfo.setNumTotalPrice(stock.multiply(item.getTaxPrice()).setScale(2, RoundingMode.HALF_UP));
                //     productInfo.setChecked(false);
                //     allChecked = false;
                //     continue;
                // }
                productInfo.setResCode(200);
            }

            vo.setProductInfo(productInfos);
            vo.setChecked(allChecked);
            vos.add(vo);
        }

        return vos;
    }

    private void fillProductInfoFields(SubmitOrderProductItemInfoByPlanVO info, PlanDetail item, String shopName, Plan plan, BigDecimal taxRate) {
        info.setShopName(shopName);
        info.setBillNo(plan.getBillNo());
        info.setBillId(item.getBillId());
        info.setDtlId(item.getDtlId());
        info.setStorageId(item.getStorageId());
        info.setStorageName(item.getStorageName());
        info.setStorageOrgId(item.getStorageOrgId());
        info.setCreditCode(item.getCreditCode());
        info.setShortCode(item.getOrgShort());

        info.setChecked(true);

        BigDecimal residueNum = new BigDecimal(item.getNotConsumeNumber());
        BigDecimal sellPrice = item.getPrice();
        
        info.setPaymentPeriod(item.getPaymentPeriod());
        info.setTaxRate(taxRate);
        info.setSellPrice(sellPrice);
        info.setPrice(sellPrice);
        info.setCartNum(residueNum);
        info.setCartMaxNum(residueNum);
        info.setNumTotalPrice(residueNum.multiply(sellPrice).setScale(2, RoundingMode.HALF_UP));

        info.setRegionPriceId(item.getRegionPriceId());
        info.setCostPrice(item.getCostPrice());
    }

    private void markError(SubmitOrderProductItemInfoByPlanVO detail, String cause) {
        detail.setCause(cause);
        detail.setResCode(50010);
    }
}
