package scrbg.meplat.mall.service.impl;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import scrbg.meplat.mall.entity.*;
import scrbg.meplat.mall.exception.BusinessException;
import scrbg.meplat.mall.mapper.SupplierReconciliationDtlMapper;
import scrbg.meplat.mall.service.*;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;

import com.scrbg.common.utils.PageUtils;
import com.scrbg.common.utils.Query;
import scrbg.meplat.mall.util.TaxCalculator;
import scrbg.meplat.mall.util.ThreadLocalUtil;
import scrbg.meplat.mall.vo.supplier.SupplierReconciliationVo;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @描述：物资验收明细 服务类
 * @作者: ye
 * @日期: 2023-08-15
 */
@Service
public class SupplierReconciliationDtlServiceImpl extends ServiceImpl<SupplierReconciliationDtlMapper, SupplierReconciliationDtl> implements SupplierReconciliationDtlService {
    @Autowired
    private OrderShipDtlService orderShipDtlService;
    @Autowired
    private OrderItemService orderItemService;

    @Autowired
    private SupplierReconciliationService supplierReconciliationService;

    @Autowired
    private OrderReturnItemService orderReturnItemService;

    @Autowired
    SupplierReconciliationDtlMapper dtlMapper;


    @Override
    public PageUtils queryPage(JSONObject jsonObject, LambdaQueryWrapper<SupplierReconciliationDtl> queryWrapper) {
        IPage<SupplierReconciliationDtl> page = this.page(
                new Query<SupplierReconciliationDtl>().getPage(jsonObject),
                queryWrapper
        );
        return new PageUtils(page);
    }


    @Override
    public void create(SupplierReconciliationDtl supplierReconciliationDtl) {
        //调用父类方法即可
        //也可以baseMapper.insert
        super.save(supplierReconciliationDtl);
    }

    @Override
    public void update(SupplierReconciliationDtl supplierReconciliationDtl) {
        super.updateById(supplierReconciliationDtl);
    }


    @Override
    public SupplierReconciliationDtl getById(String id) {
        return super.getById(id);
    }

    @Override
    public void delete(String id) {
        //作废退货单项和发货单项
        cancellation(id);
        super.removeById(id);
    }

    @Override
    public void cancellation(String id) {
        SupplierReconciliationDtl dtl = getById(id);
        if (StringUtils.isNotBlank(dtl.getSourceDtlId())) {
            if (dtl.getReconciliationType() == 1) {
                if (StringUtils.isNotBlank(dtl.getSourceDtlId())) {
                    OrderShipDtl byId = orderShipDtlService.getDataByIdAndIsReconciliation(dtl.getSourceDtlId(), 1);
                    dtl.setNoRatePrice(byId.getOtherProductPrice());
                    if (byId != null) {
                        byId.setIsReconciliation(0);
                        orderShipDtlService.update(byId);
                    } else {
                        throw new BusinessException(500, "发货源单不存在或者源单已对账");
                    }
                }
            }
            if (dtl.getReconciliationType() == 2) {
                OrderReturnItem byId = orderReturnItemService.getDataByIdAndIsReconciliation(dtl.getSourceDtlId(), 1);
                if (byId != null) {
                    dtl.setNoRatePrice(byId.getOtherProductPrice());
                    byId.setIsReconciliation(0);
                    orderReturnItemService.update(byId);
                } else {
                    throw new BusinessException(500, "退货源单不存在或者源单已对账");
                }
            }
        }
    }

    @Override
    public void deleteBatch(List<String> ids) {
        super.removeByIds(ids);
    }

    /**
     * 查询发货单和退货单可生成的二级对账单
     *
     * @param jsonObject
     * @param q
     * @return
     */
    @Override
    public PageUtils getReconciliationDtlList(JSONObject jsonObject, QueryWrapper<SupplierReconciliationDtl> q) {
        Map<String, Object> innerMap = jsonObject.getInnerMap();
        String startTime = (String) innerMap.get("startTime");
        String endTime = (String) innerMap.get("endTime");
        String keywords = (String) innerMap.get("keywords");
        List<String> orderIds = (List<String>) innerMap.get("orderIds");
        Integer sourceType = (Integer) innerMap.get("sourceType");
        Integer billType = (Integer) innerMap.get("billType");
        Integer userType = (Integer) innerMap.get("userType");
        String twoSupplierOrgId = (String) innerMap.get("twoSupplierOrgId");
        if (StringUtils.isNotBlank(keywords)) {
            q.and((t) -> {
                t.like("order_sn", keywords)
                .or()
                .like("res.product_name", keywords);
            });
        }
        if (orderIds != null && orderIds.size() > 0) {
            q.in("res.order_Id", orderIds);
        }
        if (userType == 1) {
            q.eq("res.supplierOrgId", ThreadLocalUtil.getCurrentUser().getEnterpriseId());
            if (StringUtils.isNotBlank(twoSupplierOrgId)) {
                q.eq("res.twoSupplierOrgId", twoSupplierOrgId);
            }
        } else if (userType == 2) {
            String supplierId = (String) innerMap.get("supplierOrgId");
            if (StringUtils.isNotBlank(supplierId)) {
                q.eq("res.supplierOrgId", supplierId);
            }
            q.eq("res.twoSupplierOrgId", ThreadLocalUtil.getCurrentUser().getEnterpriseId());
        }
        if (StringUtils.isNotBlank(startTime)) {
            q.gt("res.receivingDate", startTime);
        }
        if (StringUtils.isNotBlank(endTime)) {
            q.lt("res.receivingDate", endTime);
        }

        q.eq(sourceType != null, "res.source_type", sourceType);
        q.eq(billType != null, "res.bill_type", billType);
        q.eq("res.is_reconciliation", 0);
        IPage<SupplierReconciliationDtl> pages = new Query<SupplierReconciliationDtl>().getPage(jsonObject);

        List<SupplierReconciliationDtl> list = baseMapper.findByCondition(pages, q);
        for (SupplierReconciliationDtl supplierReconciliationDtl : list) {
            OrderItem byId = orderItemService.lambdaQuery()
                    .eq(OrderItem::getOrderItemId, supplierReconciliationDtl.getOrderItemId())
                    .select(OrderItem::getRelevanceName).one();
            if(byId != null) {
                supplierReconciliationDtl.setMaterialName(byId.getRelevanceName());
                BigDecimal actualTotalAmount = supplierReconciliationDtl.getTotalAmount();
                actualTotalAmount = actualTotalAmount != null ? actualTotalAmount : BigDecimal.ZERO;
                BigDecimal actualNoRateAmount  = supplierReconciliationDtl.getNoRateAmount();
                actualNoRateAmount = actualNoRateAmount != null ? actualNoRateAmount : BigDecimal.ZERO;
                BigDecimal taxAmount = actualTotalAmount //计算总税额
                        .subtract(actualNoRateAmount)
                        .setScale(2, RoundingMode.HALF_UP);
                supplierReconciliationDtl.setTaxAmount(taxAmount);
            }
        }
        PageUtils pageUtils = new PageUtils(pages);
        pageUtils.setList(list);
        return pageUtils;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveList(SupplierReconciliation dto, List<SupplierReconciliationDtl> dtls) {
        if (dtls != null && dtls.size() > 0) {

            BigDecimal noRateAmount = new BigDecimal(0);
            BigDecimal rateAmount = new BigDecimal(0);
            ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();
            ArrayList<OrderReturnItem> orderRuternItems = new ArrayList<>();

            for (SupplierReconciliationDtl dtl : dtls) {
                dtl.setBillId(dto.getBillId());
                dtl.setBillNo(dto.getBillNo());
                if (dtl.getReconciliationType() == 1) {
                    if (StringUtils.isNotBlank(dtl.getSourceDtlId())) {
                        OrderShipDtl byId = orderShipDtlService.getDataByIdAndIsReconciliation(dtl.getSourceDtlId(), 0);
//                        dtl.setRatePrice(byId.getOtherProductPrice());
                        if (byId != null) {
                            byId.setIsReconciliation(1);
                            shipDtls.add(byId);
                        } else {
                            throw new BusinessException(500, "发货源单不存在或者源单已对账");
                        }
                    }
                }
                if (dtl.getReconciliationType() == 2) {
                    OrderReturnItem byId = orderReturnItemService.getDataByIdAndIsReconciliation(dtl.getSourceDtlId(), 0);
                    if (byId != null) {
//                        dtl.setNoRatePrice(byId.getOtherProductPrice());
                        byId.setIsReconciliation(1);
                        orderRuternItems.add(byId);
                    } else {
                        throw new BusinessException(500, "退货源单不存在或者源单已对账");
                    }
                }
            }
            supplierReconciliationService.updateById(dto);
            orderShipDtlService.updateBatchById(shipDtls);
            orderReturnItemService.updateBatchById(orderRuternItems);
            saveBatch(dtls);
        } else {
            throw new BusinessException(500, "对账单明细不能为空");
        }
    }

    //获取二级对账单物资价格
    private BigDecimal getDtlprice(SupplierReconciliation dto, SupplierReconciliationDtl dtl) {
        BigDecimal price = BigDecimal.valueOf(0);
        //对账类型（1浮动价格对账单2固定价格对账单）
        if (dto.getType() == 1) {
            BigDecimal netPrice = dtl.getNetPrice();
            BigDecimal fixationPrice = dtl.getFixationPrice();
            price = netPrice.add(fixationPrice);
        } else if (dto.getType() == 2) {
            //业务类型（1合同2计划3调拨4甲供5暂估6大宗临购 大宗月供没有固定价格和出厂价
            if (dto.getBusinessType() == 6) {
                BigDecimal outFactoryPrice = dtl.getOutFactoryPrice();
                BigDecimal transportPrice = dtl.getTransportPrice();
                if (transportPrice == null) {
                    transportPrice = BigDecimal.valueOf(0);
                    dtl.setTransportPrice(transportPrice);
                }
                if(outFactoryPrice != null) {
                    price = outFactoryPrice.add(transportPrice);
                }
            } else {
                price = dtl.getPrice();
            }

        }
        return price;
    }

    @Override
    public List<SupplierReconciliationDtl> getListByBillId(String billId) {
        return lambdaQuery().eq(SupplierReconciliationDtl::getBillId, billId).list();
    }

    /**
     * 根据对账单ID作废对账明细数据
     * 该方法用于在对账单作废时，将对账明细关联的源单据状态恢复为未对账状态，
     * 确保数据一致性和业务流程的正确性。
     * 处理逻辑：
     * 1. 查询指定对账单下的所有对账明细
     * 2. 根据对账类型分别处理：
     * - 对账类型=1（发货对账）：恢复发货明细的对账状态
     * - 对账类型=2（退货对账）：恢复退货明细的对账状态
     * 3. 批量更新源单据的对账状态为未对账（isReconciliation=0）
     * @param billId 对账单ID，用于查询需要作废的对账明细
     * @throws BusinessException 当源单不存在或源单已对账时抛出业务异常
     */
    @Override
    public void nullifyDataByBillId(String billId) {
        // 获取指定对账单下的所有对账明细
        List<SupplierReconciliationDtl> list = getListByBillId(billId);
        if (list != null && list.size() > 0) {
            // 用于批量更新的集合
            ArrayList<OrderShipDtl> shipDtls = new ArrayList<>();        // 发货明细集合
            ArrayList<OrderReturnItem> orderRuternItems = new ArrayList<>();  // 退货明细集合

            // 遍历对账明细，根据对账类型进行不同处理
            for (SupplierReconciliationDtl dtl : list) {
                // 处理发货对账类型（reconciliationType = 1）
                if (dtl.getReconciliationType() == 1) {
                    if (StringUtils.isNotBlank(dtl.getSourceDtlId())) {
                        // 查询对应的发货明细，确保该明细当前处于已对账状态
                        OrderShipDtl byId = orderShipDtlService.getDataByIdAndIsReconciliation(dtl.getSourceDtlId(), 1);
                        dtl.setNoRatePrice(byId.getOtherProductPrice());
                        if (byId != null) {
                            // 将发货明细的对账状态恢复为未对账
                            byId.setIsReconciliation(0);
                            shipDtls.add(byId);
                        } else {
                            throw new BusinessException(500, "发货源单不存在或者源单已对账");
                        }
                    }
                } else if (dtl.getReconciliationType() == 2) {// 处理退货对账类型（reconciliationType = 2）
                    // 查询对应的退货明细，确保该明细当前处于已对账状态
                    OrderReturnItem byId = orderReturnItemService.getDataByIdAndIsReconciliation(dtl.getSourceDtlId(), 1);
                    if (byId != null) {
                        dtl.setNoRatePrice(byId.getOtherProductPrice());
                        // 将退货明细的对账状态恢复为未对账
                        byId.setIsReconciliation(0);
                        orderRuternItems.add(byId);
                    } else {
                        throw new BusinessException(500, "退货源单不存在或者源单已对账");
                    }
                }
            }

            // 批量更新发货明细和退货明细的对账状态
            orderShipDtlService.updateBatchById(shipDtls);
            orderReturnItemService.updateBatchById(orderRuternItems);
        }
    }

    /**
     * 根据对账单ID批量查询收料明细,对账单导出模板内容
     *
     * @param billId 对账单ID
     * @return 导出对账单明细列表
     */
    @Override
    public List<SupplierReconciliationDtlExcel> getReceiptDetailByBillId(String billId) {
        return dtlMapper.getReceiptDetailByBillId(billId);
    }
}
