package scrbg.meplat.mall.entity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import scrbg.meplat.mall.entity.parent.MustBaseEntity;

/**
 * @描述：竞价中标记录
 * @作者: ye
 * @日期: 2023-07-19
 */
@ApiModel(value = "竞价中标记录")
@Data
@TableName("bidding_win_record")
public class BiddingWinRecord extends MustBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "竞价中标记录id")
    private String winRecordId;

    @ApiModelProperty(value = "竞价记录id")

    private String bidRecordId;


    @ApiModelProperty(value = "竞价采购id")

    private String biddingId;


    @ApiModelProperty(value = "供应商id")

    private String supplierId;


    @ApiModelProperty(value = "供应商名称")

    private String supplierName;


    @ApiModelProperty(value = "不含税总金额")

    private BigDecimal bidAmount;


    @ApiModelProperty(value = "含税总金额")

    private BigDecimal bidRateAmount;


    @ApiModelProperty(value = "中标时间")

    private Date winTime;


    @ApiModelProperty(value = "状态")

    private Integer state;


    @ApiModelProperty(value = "驳回原因")

    private String rejectReason;


    @ApiModelProperty(value = "创建机构id")

    private String createOrgId;

    @ApiModelProperty(value = "创建机构名称")

    private String createOrgName;

    private String synthesizeTemporarySn;



}