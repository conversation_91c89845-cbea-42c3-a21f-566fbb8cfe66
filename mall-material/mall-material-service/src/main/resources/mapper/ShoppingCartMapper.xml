<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.ShoppingCartMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="scrbg.meplat.mall.entity.ShoppingCart" id="ShoppingCartMap">
        <result property="cartId" column="cart_id"/>
        <result property="productId" column="product_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="shopId" column="shop_id"/>
        <result property="userId" column="user_id"/>
        <result property="cartNum" column="cart_num"/>
        <result property="cartTime" column="cart_time"/>
        <result property="productPrice" column="product_price"/>
        <result property="skuProps" column="sku_props"/>
        <result property="isDelete" column="is_delete"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="founderId" column="founder_id"/>
        <result property="founderName" column="founder_name"/>
        <result property="productType" column="product_type"/>
        <result property="remarks" column="remarks"/>
        <result property="sort" column="sort"/>
        <result property="state" column="state"/>
        <result property="mallType" column="mall_type"/>
        <result property="cartImg" column="cart_img"/>
    </resultMap>
    
    <resultMap id="ShoppingCartVOMap1" type="scrbg.meplat.mall.vo.product.ShoppingCartVO">
        <id column="cart_id" jdbcType="CHAR" property="cartId"/>
        <result column="product_id" jdbcType="CHAR" property="productId"/>
        <result column="sku_id" jdbcType="CHAR" property="skuId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="cart_num" jdbcType="DECIMAL" property="cartNum"/>
        <result column="cart_time" jdbcType="DATE" property="cartTime"/>
        <result column="product_price" jdbcType="DECIMAL" property="productPrice"/>
        <result column="cost_price" jdbcType="DECIMAL" property="costPrice"/>
        <result column="sku_props" jdbcType="VARCHAR" property="skuProps"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="medium_url" jdbcType="VARCHAR" property="productImg"/>
        <result column="original_price" jdbcType="DECIMAL" property="originalPrice"/>
        <result column="sell_price" jdbcType="DECIMAL" property="sellPrice"/>
        <result column="sku_name" jdbcType="VARCHAR" property="spec"/>
        <result column="stock" jdbcType="DECIMAL" property="stock"/>
    </resultMap>

    <!-- 根据购车id获取商品信息（只适合单个商品捆绑商品有效（废弃））-->
    <select id="listShoppingCartInfoByIds" resultMap="ShoppingCartVOMap1">
        select c.cart_id, c.product_id, c.sku_id, c.user_id, c.cart_num, c.cart_time,
        c.product_price, c.sku_props, p.product_name,
        i.medium_url,s.cost_price,s.original_price,s.sell_price,s.sku_name,s.stock
        from shopping_cart c
        INNER JOIN product p
        INNER JOIN product_medium i
        INNER JOIN product_sku s
        ON c.product_id = p.product_id
        and i.product_id=p.product_id
        and c.sku_id=s.sku_id
        where i.is_main=1 and c.cart_id in
        <foreach collection="cartIdList" item="cid" separator="," open="(" close=")">
            #{cid}
        </foreach>
    </select>

    <resultMap id="listProductIdAndCartByCartIdsMap" type="scrbg.meplat.mall.vo.product.ShoppingCartProductInfoVO">
        <result column="product_id" property="productId"/>
        <collection property="shoppingCarts" ofType="scrbg.meplat.mall.entity.ShoppingCart">
            <result property="cartId" column="cart_id"/>
            <result property="shopId" column="shop_id"/>
            <result property="productId" column="product_id"/>
            <result property="skuId" column="sku_id"/>
            <result property="userId" column="user_id"/>
            <result property="cartNum" column="cart_num"/>
            <result property="cartTime" column="cart_time"/>
            <result property="productPrice" column="product_price"/>
            <result property="skuProps" column="sku_props"/>
            <result property="isDelete" column="is_delete"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
            <result property="founderId" column="founder_id"/>
            <result property="founderName" column="founder_name"/>
            <result property="productType" column="product_type"/>
            <result property="remarks" column="remarks"/>
            <result property="sort" column="sort"/>
            <result property="state" column="state"/>
            <result property="mallType" column="mall_type"/>
        </collection>
    </resultMap>

    <!--根据购物车id获取商品id和skuId-->
    <select id="listProductIdAndCartByCartIds" resultMap="listProductIdAndCartByCartIdsMap">
        select c.product_id,c.*
        from shopping_cart c
        where c.is_delete = 0 and c.product_type = #{productType} and c.user_id = #{userId} and c.cart_id in
        <foreach collection="cartIds" item="cid" separator="," open="(" close=")">
            #{cid}
        </foreach>
    </select>

    <resultMap id="listShopIdAndCartByCartIdsMap" type="scrbg.meplat.mall.vo.product.ShoppingCartShopIdVO">
        <result column="shop_id" property="shopId"/>
        <collection property="shoppingCarts" ofType="scrbg.meplat.mall.entity.ShoppingCart">
            <result property="cartId" column="cart_id"/>
            <result property="shopId" column="shop_id"/>
            <result property="productId" column="product_id"/>
            <result property="skuId" column="sku_id"/>
            <result property="userId" column="user_id"/>
            <result property="cartNum" column="cart_num"/>
            <result property="cartTime" column="cart_time"/>
            <result property="productPrice" column="product_price"/>
            <result property="regionPriceId" column="region_price_id"/>
            <result property="noTaxPrice" column="no_tax_price"/>
            <result property="skuProps" column="sku_props"/>
            <result property="isDelete" column="is_delete"/>
            <result property="gmtCreate" column="gmt_create"/>
            <result property="gmtModified" column="gmt_modified"/>
            <result property="founderId" column="founder_id"/>
            <result property="founderName" column="founder_name"/>
            <result property="productType" column="product_type"/>
            <result property="remarks" column="remarks"/>
            <result property="taxRate" column="tax_rate"/>
            <result property="sort" column="sort"/>
            <result property="state" column="state"/>
            <result property="mallType" column="mall_type"/>
            <result property="leaseNum" column="lease_num"/>
            <result property="leaseUnit" column="lease_unit"/>
            <result property="paymentPeriod" column="payment_period"/>
            <result property="zoneAddr" column="zone_addr"/>
            <result property="zoneId" column="zone_id"/>
        </collection>
    </resultMap>
    <!--    根据购物车id获取商品id和skuId-->
    <select id="listShopIdAndCartByCartIds" resultMap="listShopIdAndCartByCartIdsMap">
        select c.shop_id,c.*
        from shopping_cart c
        where c.is_delete = 0 and c.user_id = #{userId} and c.cart_id in
        <foreach collection="cartIds" item="cid" separator="," open="(" close=")">
            #{cid}
        </foreach>
    </select>

    <resultMap id="getMyCartListMap" type="scrbg.meplat.mall.vo.product.website.WCartInfoVO">
        <result column="shop_id" property="shopId"/>
        <result column="shop_name" property="shopName"/>
        <result column="shopState" property="shopState"/>
        <collection property="productInfo" ofType="scrbg.meplat.mall.vo.product.website.WCartItemVO">
            <result property="cartId" column="cart_id"/>
            <result property="productId" column="product_id"/>
            <result property="skuId" column="sku_id"/>
            <result property="cartNum" column="cart_num"/>
            <result property="cartTime" column="cart_time"/>
            <result property="zoneAddr" column="zone_addr"/>
            <result property="zoneId" column="zone_id"/>
            <result property="checked" column="checked"/>
            <result property="skuProps" column="sku_props"/>
            <result property="productMinImg" column="product_min_img"/>
            <result property="productPrice" column="product_price"/>
            <result property="noTaxPrice" column="no_tax_price"/>
            <result property="productName" column="product_name"/>
            <result property="regionPriceId" column="region_price_id"/>
            <result property="leaseNum" column="lease_num"/>
            <result property="classId" column="class_id"/>
            <result property="brandId" column="brand_id"/>
            <result property="productType" column="product_type"/>
            <result property="priceType" column="price_type"/>
            <result property="state" column="state"/>
            <result property="taxRate" column="tax_rate"/>
            <result property="paymentPeriod" column="payment_period"/>
            <result property="productMinPrice" column="product_min_price"/>
            <result property="productKeyword" column="product_keyword"/>
            <result property="productTransportType" column="product_transport_type"/>
            <collection property="regionPriceList" ofType="scrbg.meplat.mall.entity.RegionPrice">
                <result property="regionPriceId" column="rp_region_price_id"/>
                <result property="productId" column="rp_product_id"/>
                <result property="regionName" column="rp_region_name"/>
                <result property="area" column="rp_area"/>
                <result property="areaCode" column="rp_area_code"/>
                <result property="taxInPrice" column="rp_tax_in_price"/>
                <result property="price" column="rp_price"/>
                <result property="accountPeriod" column="rp_account_period"/>
                <result property="payBeforeDelivery" column="rp_pay_before_delivery"/>
                <result property="annualizedRate" column="rp_annualized_rate"/>
                <result property="markUpNum" column="rp_mark_up_num"/>
                <result property="bonusTaxInPrice" column="rp_bonus_tax_in_price"/>
                <result property="bonusPrice" column="rp_bonus_price"/>
            </collection>
        </collection>
    </resultMap>

    <!-- 根据用户id 用户购物车列表 -->
    <select id="getMyCartList" resultMap="getMyCartListMap">
        select s.shop_id,
               s.shop_name,
               s.state as shopState,
               sc.checked,
               sc.zone_addr,
               sc.zone_id,
               sc.cart_id,
               sc.product_price,
               sc.no_tax_price,
               sc.product_id,
               sc.sku_id,
               sc.cart_num,
               sc.cart_time,
               sc.sku_props,
               sc.lease_num,
               p.product_name,
               sc.region_price_id,
               p.class_id,
               p.brand_id,
               p.product_type,
               p.price_type,
               p.state,
               p.product_min_price,
               p.product_keyword,
               p.product_transport_type,
               p.product_min_img,
               sc.tax_rate,
               sc.payment_period,
               rp.region_price_id as rp_region_price_id,
               rp.product_id as rp_product_id,
               rp.region_name as rp_region_name,
               rp.area as rp_area,
               rp.area_code as rp_area_code,
               rp.tax_in_price as rp_tax_in_price,
               rp.price as rp_price,
               rp.account_period as rp_account_period,
               rp.pay_before_delivery as rp_pay_before_delivery,
               rp.annualized_rate as rp_annualized_rate,
               rp.mark_up_num as rp_mark_up_num,
               rp.bonus_tax_in_price as rp_bonus_tax_in_price,
               rp.bonus_price as rp_bonus_price
        from shopping_cart sc
                 inner join shop s on sc.shop_id = s.shop_id
                 inner join product p on sc.product_id = p.product_id
                 left join region_price rp on p.product_id = rp.product_id and rp.is_delete = 0
        <where>
            sc.user_id = #{userId} and sc.is_delete=0 and sc.mall_type = #{mallType} and p.state = 1 and p.show_state = 0
            <if test="productType != null">
                and sc.product_type = #{productType};
            </if>
        </where>
    </select>

    <select id="getMyCartListCount" resultType="java.lang.Integer">
        select count(*)
        from shopping_cart sc
        inner join shop s on sc.shop_id = s.shop_id
        inner join product p on sc.product_id = p.product_id
        <where>
            sc.user_id = #{userId} and sc.is_delete=0 and sc.mall_type = #{mallType} and p.state = 1 and p.show_state = 0
            <if test="productType != null">
                and sc.product_type = #{productType};
            </if>
        </where>
    </select>
</mapper>