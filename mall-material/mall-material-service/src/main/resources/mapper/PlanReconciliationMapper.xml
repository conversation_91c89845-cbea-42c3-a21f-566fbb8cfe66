<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="scrbg.meplat.mall.mapper.PlanReconciliationMapper">

    <!-- 供应商方-查询可对账的计划订单列表总数 -->
    <select id="getReconciliablePlansBySupplierCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT o.order_sn)
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_id = d.order_id
        INNER JOIN order_ship os ON d.order_id = os.order_id
        INNER JOIN order_select_plan osp ON o.order_id = osp.order_id
        LEFT JOIN enterprise_info receive_org ON os.receive_org_id = receive_org.enterprise_id
        LEFT JOIN enterprise_info supplier_org ON o.supplier_id = supplier_org.enterprise_id
        LEFT JOIN plan p ON p.bill_no = osp.bill_no

        -- 已对账发货数量统计（is_reconciliation=1的发货）
        LEFT JOIN (
        SELECT
        order_id,
        product_id,
        SUM(ship_num) as reconciled_ship_quantity
        FROM order_ship_dtl
        WHERE is_delete = 0
        AND is_reconciliation = 1  -- 已对账状态
        GROUP BY order_id, product_id
        ) reconciled_ship ON o.order_id = reconciled_ship.order_id AND d.product_id = reconciled_ship.product_id

        -- 已完成对账的退货数量统计（is_reconciliation=1的退货）
        LEFT JOIN (
        SELECT
        order_id,
        product_id,
        SUM(count) as reconciled_return_quantity
        FROM order_return_item
        WHERE is_delete = 0
        AND is_reconciliation = 1  -- 已对账状态
        GROUP BY order_id, product_id
        ) reconciled_return ON o.order_id = reconciled_return.order_id AND d.product_id = reconciled_return.product_id

        -- 对账中的物资数量统计（对账单中非作废非删除状态的物资）
        LEFT JOIN (
        SELECT
        mrd.order_id,
        mrd.material_id,
        SUM(mrd.quantity) as in_reconciliation_quantity
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        WHERE mrd.is_delete = 0
        AND mr.is_delete != -1  -- 未被逻辑删除
        AND mr.state != 7       -- 非作废状态
        GROUP BY mrd.order_id, mrd.material_id
        ) in_reconciliation ON o.order_id = in_reconciliation.order_id AND d.product_id = in_reconciliation.material_id

        WHERE o.is_delete = 0
        AND d.is_delete = 0
        AND os.is_delete = 0
        AND os.type = 2
        AND d.ship_num > 0
        AND d.receive_time IS NOT NULL
        AND d.is_reconciliation = 0 -- 排除已完成对账的发货明细
        -- 可对账数量 = 发货数量 - 已对账发货 - 已完成对账退货 - 对账中的物资
        AND (d.ship_num
        - IFNULL(reconciled_ship.reconciled_ship_quantity, 0)      -- 已对账发货
        - IFNULL(reconciled_return.reconciled_return_quantity, 0)  -- 已完成对账退货
        - IFNULL(in_reconciliation.in_reconciliation_quantity, 0)) > 0 -- 对账中的物资
        <if test="dto.supplierId != null and dto.supplierId != ''">
            AND o.supplier_id = #{dto.supplierId}
        </if>
        <if test="dto.billNo != null and dto.billNo != ''">
            AND o.plan_no LIKE CONCAT('%', #{dto.billNo}, '%')
        </if>
        <if test="dto.orderSn != null and dto.orderSn != ''">
            AND o.order_sn IN
            <foreach collection="dto.orderSn.split(',')" item="sn" open="(" separator="," close=")">
                #{sn}
            </foreach>
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            AND (o.plan_no LIKE CONCAT('%', #{dto.keyWord}, '%')
            OR o.order_sn LIKE CONCAT('%', #{dto.keyWord}, '%')
            OR COALESCE(receive_org.enterprise_name, o.receiver_name, os.receive_org_name) LIKE CONCAT('%',
            #{dto.keyWord}, '%'))
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.productType != null">
            <choose>
                <when test="dto.productType == 0">
                    AND o.product_type IN (0, 10)
                </when>
                <when test="dto.productType == 1">
                    AND o.product_type IN (1, 13)
                </when>
                <when test="dto.productType == 2">
                    AND o.product_type = 2
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
    </select>

    <!-- 供应商方-查询可对账的计划下的订单列表 -->
    <select id="getReconciliablePlansBySupplier" resultType="scrbg.meplat.mall.vo.reconciliation.PlanReconciliationVO">
        SELECT DISTINCT
        osp.bill_no as bill_no,
        o.order_sn as order_sn,
        receive_org.enterprise_name as org_name,
        o.supplier_name,
        p.p_bill_no as p_bill_no,
        p.p_bill_id as p_bill_id,
        p.tax_rate as tax_rate,
        o.gmt_create as gmt_create
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_id = d.order_id
        INNER JOIN order_ship os ON d.order_id = os.order_id
        INNER JOIN order_select_plan osp ON o.order_id = osp.order_id
        LEFT JOIN enterprise_info receive_org ON os.receive_org_id = receive_org.enterprise_id
        LEFT JOIN enterprise_info supplier_org ON o.supplier_id = supplier_org.enterprise_id
        LEFT JOIN plan p ON p.bill_no = osp.bill_no

        -- 已对账发货数量统计（is_reconciliation=1的发货）
        LEFT JOIN (
        SELECT
        order_id,
        product_id,
        SUM(ship_num) as reconciled_ship_quantity
        FROM order_ship_dtl
        WHERE is_delete = 0
        AND is_reconciliation = 1  -- 已对账状态
        GROUP BY order_id, product_id
        ) reconciled_ship ON o.order_id = reconciled_ship.order_id AND d.product_id = reconciled_ship.product_id

        -- 已完成对账的退货数量统计（is_reconciliation=1的退货）
        LEFT JOIN (
        SELECT
        order_id,
        product_id,
        SUM(count) as reconciled_return_quantity
        FROM order_return_item
        WHERE is_delete = 0
        AND is_reconciliation = 1  -- 已对账状态
        GROUP BY order_id, product_id
        ) reconciled_return ON o.order_id = reconciled_return.order_id AND d.product_id = reconciled_return.product_id

        -- 对账中的物资数量统计（对账单中非作废非删除状态的物资）
        LEFT JOIN (
        SELECT
        mrd.order_id,
        mrd.material_id,
        SUM(mrd.quantity) as in_reconciliation_quantity
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        WHERE mrd.is_delete = 0
        AND mr.is_delete != -1  -- 未被逻辑删除
        AND mr.state != 7       -- 非作废状态
        GROUP BY mrd.order_id, mrd.material_id
        ) in_reconciliation ON o.order_id = in_reconciliation.order_id AND d.product_id = in_reconciliation.material_id

        WHERE o.is_delete = 0
        AND d.is_delete = 0
        AND os.is_delete = 0
        AND os.type = 2
        AND d.ship_num > 0
        AND d.receive_time IS NOT NULL
        AND d.is_reconciliation = 0 -- 排除已完成对账的发货明细
        -- 可对账数量 = 发货数量 - 已对账发货 - 已完成对账退货 - 对账中的物资
        AND (d.ship_num
        - IFNULL(reconciled_ship.reconciled_ship_quantity, 0)      -- 已对账发货
        - IFNULL(reconciled_return.reconciled_return_quantity, 0)  -- 已完成对账退货
        - IFNULL(in_reconciliation.in_reconciliation_quantity, 0)) > 0 -- 对账中的物资
        <if test="dto.supplierId != null and dto.supplierId != ''">
            AND o.supplier_id = #{dto.supplierId}
        </if>
        <if test="dto.billNo != null and dto.billNo != ''">
            AND o.plan_no LIKE CONCAT('%', #{dto.billNo}, '%')
        </if>
        <if test="dto.orderSn != null and dto.orderSn != ''">
            AND o.order_sn IN
            <foreach collection="dto.orderSn.split(',')" item="sn" open="(" separator="," close=")">
                #{sn}
            </foreach>
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            AND (o.plan_no LIKE CONCAT('%', #{dto.keyWord}, '%')
            OR o.order_sn LIKE CONCAT('%', #{dto.keyWord}, '%')
            OR receive_org.enterprise_name LIKE CONCAT('%', #{dto.keyWord}, '%'))
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.productType != null">
            <choose>
                <when test="dto.productType == 0">
                    AND o.product_type IN (0, 10)
                </when>
                <when test="dto.productType == 1">
                    AND o.product_type IN (1, 13)
                </when>
                <when test="dto.productType == 2">
                    AND o.product_type = 2
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
        ORDER BY o.gmt_create DESC
        <!-- 手动分页 -->
        <if test="dto.limit != null and dto.limit > 0">
            LIMIT ${(dto.page - 1) * dto.limit}, ${dto.limit}
        </if>
    </select>

    <!-- 供应商方-查询可对账的项目部总数 -->
    <select id="getReconciliableEnterprisePageListCount" resultType="java.lang.Integer">
        SELECT IFNULL(COUNT(DISTINCT receive_org.enterprise_name), 0) as count
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_id = d.order_id
        INNER JOIN order_ship os ON d.bill_id = os.bill_id
        LEFT JOIN enterprise_info receive_org ON os.receive_org_id = receive_org.enterprise_id
        LEFT JOIN enterprise_info supplier_org ON o.supplier_id = supplier_org.enterprise_id
        LEFT JOIN (
        SELECT
        order_id,
        material_id,
        SUM(quantity) AS reconciled_quantity
        FROM material_reconciliation_dtl
        WHERE is_delete = 0
        GROUP BY order_id, material_id
        ) reconciled ON o.order_id = reconciled.order_id AND d.product_id = reconciled.material_id
        WHERE o.is_delete = 0
        AND d.is_delete = 0
        AND os.is_delete = 0
        AND os.type = 2
        AND d.ship_num > 0
        AND d.receive_time IS NOT NULL
        AND d.is_reconciliation = 0
        AND (d.ship_num - IFNULL(reconciled.reconciled_quantity, 0)) > 0
        AND receive_org.enterprise_name IS NOT NULL
        <if test="dto.supplierId != null and dto.supplierId != ''">
            AND o.supplier_id = #{dto.supplierId}
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.productType != null">
            <choose>
                <when test="dto.productType == 0">
                    AND o.product_type IN (0, 10)
                </when>
                <when test="dto.productType == 1">
                    AND o.product_type IN (1, 13)
                </when>
                <when test="dto.productType == 2">
                    AND o.product_type = 2
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            AND receive_org.enterprise_name LIKE CONCAT('%', #{dto.keyWord}, '%')
        </if>
    </select>

    <!-- 供应商方-查询可对账的项目部列表 -->
    <select id="getReconciliableEnterprisePageList"
            resultType="scrbg.meplat.mall.vo.reconciliation.PlanReconciliationVO">
        SELECT
        receive_org.enterprise_name AS org_name,
        receive_org.enterprise_id as org_id,
        MAX(DATE(o.gmt_create)) AS gmt_create
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_id = d.order_id
        INNER JOIN order_ship os ON d.bill_id = os.bill_id
        LEFT JOIN enterprise_info receive_org ON os.receive_org_id = receive_org.enterprise_id
        LEFT JOIN enterprise_info supplier_org ON o.supplier_id = supplier_org.enterprise_id
        LEFT JOIN (
        SELECT
        order_id,
        material_id,
        SUM(quantity) AS reconciled_quantity
        FROM material_reconciliation_dtl
        WHERE is_delete = 0
        GROUP BY order_id, material_id
        ) reconciled ON o.order_id = reconciled.order_id AND d.product_id = reconciled.material_id
        WHERE o.is_delete = 0
        AND d.is_delete = 0
        AND os.is_delete = 0
        AND os.type = 2
        AND d.ship_num > 0
        AND d.receive_time IS NOT NULL
        AND d.is_reconciliation = 0
        AND (d.ship_num - IFNULL(reconciled.reconciled_quantity, 0)) > 0
        AND receive_org.enterprise_name IS NOT NULL
        <if test="dto.supplierId != null and dto.supplierId != ''">
            AND o.supplier_id = #{dto.supplierId}
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            AND receive_org.enterprise_name LIKE CONCAT('%', #{dto.keyWord}, '%')
        </if>
        <if test="dto.productType != null">
            <choose>
                <when test="dto.productType == 0">
                    AND o.product_type IN (0, 10)
                </when>
                <when test="dto.productType == 1">
                    AND o.product_type IN (1, 13)
                </when>
                <when test="dto.productType == 2">
                    AND o.product_type = 2
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
        GROUP BY receive_org.enterprise_name, receive_org.enterprise_id
        ORDER BY gmt_create DESC
        <!-- 手动分页 -->
        <if test="dto.limit != null and dto.limit > 0">
            LIMIT ${(dto.page - 1) * dto.limit}, ${dto.limit}
        </if>
    </select>

    <!-- 采购方-查询可对账的供应商总数 -->
    <select id="getReconciliableSupplierByEnterpriseCount" resultType="java.lang.Integer">
        SELECT COUNT(*) as count
        FROM (
        SELECT DISTINCT o.supplier_name
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_sn = d.order_sn
        INNER JOIN order_ship os ON d.order_sn = os.order_sn
        LEFT JOIN (
        SELECT
        order_id,
        material_id,
        SUM(quantity) AS reconciled_quantity
        FROM material_reconciliation_dtl
        WHERE is_delete = 0
        GROUP BY order_id, material_id
        ) reconciled ON o.order_id = reconciled.order_id AND d.product_id = reconciled.material_id
        WHERE o.is_delete = 0
        AND d.is_delete = 0
        AND os.is_delete = 0
        AND os.type = 2
        AND d.ship_num > 0
        AND d.receive_time IS NOT NULL
        AND d.is_reconciliation = 0
        AND (d.ship_num - IFNULL(reconciled.reconciled_quantity, 0)) > 0
        AND o.supplier_name IS NOT NULL
        <if test="dto.supplierId != null and dto.supplierId != ''">
            AND o.enterprise_id = #{dto.supplierId}
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.productType != null">
            <choose>
                <when test="dto.productType == 0">
                    AND o.product_type IN (0, 10)
                </when>
                <when test="dto.productType == 1">
                    AND o.product_type IN (1, 13)
                </when>
                <when test="dto.productType == 2">
                    AND o.product_type = 2
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            AND o.supplier_name LIKE CONCAT('%', #{dto.keyWord}, '%')
        </if>
        ) temp_table
    </select>

    <!-- 采购方-查询可对账的供应商列表 -->
    <select id="getReconciliableSupplierByEnterprise" resultType="scrbg.meplat.mall.vo.reconciliation.PlanReconciliationVO">
        SELECT
        ranked_suppliers.supplier_name AS org_name,
        ranked_suppliers.supplier_id AS org_id
        FROM (
        SELECT
        o.supplier_name,
        o.supplier_id,
        o.gmt_create,
        ROW_NUMBER() OVER (PARTITION BY o.supplier_name, o.supplier_id ORDER BY o.gmt_create DESC) as rn
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_sn = d.order_sn
        INNER JOIN order_ship os ON d.order_sn = os.order_sn
        LEFT JOIN (
        SELECT
        order_id,
        material_id,
        SUM(quantity) AS reconciled_quantity
        FROM material_reconciliation_dtl
        WHERE is_delete = 0
        GROUP BY order_id, material_id
        ) reconciled ON o.order_id = reconciled.order_id AND d.product_id = reconciled.material_id
        WHERE o.is_delete = 0
        AND d.is_delete = 0
        AND os.is_delete = 0
        AND os.type = 2
        AND d.ship_num > 0
        AND d.receive_time IS NOT NULL
        AND d.is_reconciliation = 0
        AND (d.ship_num - IFNULL(reconciled.reconciled_quantity, 0)) > 0
        AND o.supplier_name IS NOT NULL
        <if test="dto.supplierId != null and dto.supplierId != ''">
            AND o.enterprise_id = #{dto.supplierId}
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            AND o.supplier_name LIKE CONCAT('%', #{dto.keyWord}, '%')
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
        <if test="dto.productType != null">
            <choose>
                <when test="dto.productType == 0">
                    AND o.product_type IN (0, 10)
                </when>
                <when test="dto.productType == 1">
                    AND o.product_type IN (1, 13)
                </when>
                <when test="dto.productType == 2">
                    AND o.product_type = 2
                </when>
            </choose>
        </if>
        ) ranked_suppliers
        WHERE rn = 1
        ORDER BY gmt_create DESC
        <if test="dto.page != null and dto.page > 0">
            LIMIT ${(dto.page - 1) * dto.limit}, ${dto.limit}
        </if>
    </select>

    <!-- 采购方-查询可对账的计划订单列表总数 -->
    <select id="getReconciliablePlansByEnterpriseCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT o.order_sn)
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_id = d.order_id
        INNER JOIN order_ship os ON d.order_id = os.order_id
        INNER JOIN order_select_plan osp ON o.order_id = osp.order_id
        LEFT JOIN enterprise_info receive_org ON os.receive_org_id = receive_org.enterprise_id
        LEFT JOIN enterprise_info supplier_org ON o.supplier_id = supplier_org.enterprise_id
        LEFT JOIN plan p ON p.bill_no = osp.bill_no

        -- 已对账发货数量统计（is_reconciliation=1的发货）
        LEFT JOIN (
        SELECT
        order_id,
        product_id,
        SUM(ship_num) as reconciled_ship_quantity
        FROM order_ship_dtl
        WHERE is_delete = 0
        AND is_reconciliation = 1  -- 已对账状态
        GROUP BY order_id, product_id
        ) reconciled_ship ON o.order_id = reconciled_ship.order_id AND d.product_id = reconciled_ship.product_id

        -- 已完成对账的退货数量统计（is_reconciliation=1的退货）
        LEFT JOIN (
        SELECT
        order_id,
        product_id,
        SUM(count) as reconciled_return_quantity
        FROM order_return_item
        WHERE is_delete = 0
        AND is_reconciliation = 1  -- 已对账状态
        GROUP BY order_id, product_id
        ) reconciled_return ON o.order_id = reconciled_return.order_id AND d.product_id = reconciled_return.product_id

        -- 对账中的物资数量统计（对账单中非作废非删除状态的物资）
        LEFT JOIN (
        SELECT
        mrd.order_id,
        mrd.trade_id,
        SUM(mrd.quantity) as in_reconciliation_quantity
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        WHERE mrd.is_delete = 0
        AND mr.is_delete != -1  -- 未被逻辑删除
        AND mr.state != 7       -- 非作废状态
        GROUP BY mrd.order_id, mrd.trade_id
        ) in_reconciliation ON o.order_id = in_reconciliation.order_id AND d.product_id = in_reconciliation.trade_id

        WHERE o.is_delete = 0
        AND d.is_delete = 0
        AND os.is_delete = 0
        AND os.type = 2
        AND d.ship_num > 0
        AND d.receive_time IS NOT NULL
        AND d.is_reconciliation = 0 -- 排除已完成对账的发货明细
        -- 按照公式：发货数量 - 已对账发货 - 已完成对账退货 - 对账中的物资 = 可对账物资
        AND (d.ship_num
        - IFNULL(reconciled_ship.reconciled_ship_quantity, 0)      -- 已对账发货
        - IFNULL(reconciled_return.reconciled_return_quantity, 0)  -- 已完成对账退货
        - IFNULL(in_reconciliation.in_reconciliation_quantity, 0)) > 0 -- 对账中的物资
        <if test="dto.supplierId != null and dto.supplierId != ''">
            AND o.supplier_id = #{dto.supplierId}
        </if>
        <if test="dto.billNo != null and dto.billNo != ''">
            AND o.plan_no LIKE CONCAT('%', #{dto.billNo}, '%')
        </if>
        <if test="dto.orderSn != null and dto.orderSn != ''">
            AND o.order_sn IN
            <foreach collection="dto.orderSn.split(',')" item="sn" open="(" separator="," close=")">
                #{sn}
            </foreach>
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            AND (o.plan_no LIKE CONCAT('%', #{dto.keyWord}, '%')
            OR o.order_sn LIKE CONCAT('%', #{dto.keyWord}, '%')
            OR COALESCE(receive_org.enterprise_name, o.receiver_name, os.receive_org_name) LIKE CONCAT('%',
            #{dto.keyWord}, '%'))
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.productType != null">
            <choose>
                <when test="dto.productType == 0">
                    AND o.product_type IN (0, 10)
                </when>
                <when test="dto.productType == 1">
                    AND o.product_type IN (1, 13)
                </when>
                <when test="dto.productType == 2">
                    AND o.product_type = 2
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
    </select>

    <!-- 供应商方-查询可对账的计划下的订单列表 -->
    <select id="getReconciliablePlansByEnterprise" resultType="scrbg.meplat.mall.vo.reconciliation.PlanReconciliationVO">
        SELECT DISTINCT
        osp.bill_no as bill_no,
        o.order_id as order_id,
        o.order_sn as order_sn,
        receive_org.enterprise_name as org_name,
        os.org_id as org_id,
        o.supplier_name,
        p.p_bill_no as pBillNo,
        p.p_bill_id as pBillId,
        p.tax_rate as tax_rate,
        o.gmt_create as gmt_create
        FROM orders o
        INNER JOIN order_ship_dtl d ON o.order_id = d.order_id
        INNER JOIN order_ship os ON d.order_id = os.order_id
        INNER JOIN order_select_plan osp ON o.order_id = osp.order_id
        LEFT JOIN enterprise_info receive_org ON os.receive_org_id = receive_org.enterprise_id
        LEFT JOIN enterprise_info supplier_org ON o.supplier_id = supplier_org.enterprise_id
        LEFT JOIN plan p ON p.bill_no = osp.bill_no

        -- 已对账发货数量统计（is_reconciliation=1的发货）
        LEFT JOIN (
        SELECT
        order_id,
        product_id,
        SUM(ship_num) as reconciled_ship_quantity
        FROM order_ship_dtl
        WHERE is_delete = 0
        AND is_reconciliation = 1  -- 已对账状态
        GROUP BY order_id, product_id
        ) reconciled_ship ON o.order_id = reconciled_ship.order_id AND d.product_id = reconciled_ship.product_id

        -- 已完成对账的退货数量统计（is_reconciliation=1的退货）
        LEFT JOIN (
        SELECT
        order_id,
        product_id,
        SUM(count) as reconciled_return_quantity
        FROM order_return_item
        WHERE is_delete = 0
        AND is_reconciliation = 1  -- 已对账状态
        GROUP BY order_id, product_id
        ) reconciled_return ON o.order_id = reconciled_return.order_id AND d.product_id = reconciled_return.product_id

        -- 对账中的物资数量统计（对账单中非作废非删除状态的物资）
        LEFT JOIN (
        SELECT
        mrd.order_id,
        mrd.trade_id,
        SUM(mrd.quantity) as in_reconciliation_quantity
        FROM material_reconciliation_dtl mrd
        INNER JOIN material_reconciliation mr ON mrd.reconciliation_id = mr.reconciliation_id
        WHERE mrd.is_delete = 0
        AND mr.is_delete != -1  -- 未被逻辑删除
        AND mr.state != 7       -- 非作废状态
        GROUP BY mrd.order_id, mrd.trade_id
        ) in_reconciliation ON o.order_id = in_reconciliation.order_id AND d.product_id = in_reconciliation.trade_id

        WHERE o.is_delete = 0
        AND d.is_delete = 0
        AND os.is_delete = 0
        AND os.type = 2
        AND d.ship_num > 0
        AND d.receive_time IS NOT NULL
        AND d.is_reconciliation = 0 -- 排除已完成对账的发货明细
        -- 可对账数量 = 发货数量 - 已对账发货 - 已完成对账退货 - 对账中的物资
        AND (d.ship_num
        - IFNULL(reconciled_ship.reconciled_ship_quantity, 0)      -- 已对账发货
        - IFNULL(reconciled_return.reconciled_return_quantity, 0)  -- 已完成对账退货
        - IFNULL(in_reconciliation.in_reconciliation_quantity, 0)) > 0 -- 对账中的物资
        <if test="dto.supplierId != null and dto.supplierId != ''">
            AND o.supplier_id = #{dto.supplierId}
        </if>
        <if test="dto.billNo != null and dto.billNo != ''">
            AND o.plan_no LIKE CONCAT('%', #{dto.billNo}, '%')
        </if>
        <if test="dto.orderSn != null and dto.orderSn != ''">
            AND o.order_sn IN
            <foreach collection="dto.orderSn.split(',')" item="sn" open="(" separator="," close=")">
                #{sn}
            </foreach>
        </if>
        <if test="dto.keyWord != null and dto.keyWord != ''">
            AND (o.plan_no LIKE CONCAT('%', #{dto.keyWord}, '%')
            OR o.order_sn LIKE CONCAT('%', #{dto.keyWord}, '%')
            OR receive_org.enterprise_name LIKE CONCAT('%', #{dto.keyWord}, '%'))
        </if>
        <if test="dto.startTime != null and dto.startTime != ''">
            AND d.receive_time >= #{dto.startTime}
        </if>
        <if test="dto.endTime != null and dto.endTime != ''">
            AND d.receive_time &lt;= #{dto.endTime}
        </if>
        <if test="dto.productType != null">
            <choose>
                <when test="dto.productType == 0">
                    AND o.product_type IN (0, 10)
                </when>
                <when test="dto.productType == 1">
                    AND o.product_type IN (1, 13)
                </when>
                <when test="dto.productType == 2">
                    AND o.product_type = 2
                </when>
            </choose>
        </if>
        <if test="dto.type != null and dto.type != ''">
            AND o.bill_type = #{dto.type}
        </if>
        ORDER BY o.gmt_create DESC
        <!-- 手动分页 -->
        <if test="dto.limit != null and dto.limit > 0">
            LIMIT ${(dto.page - 1) * dto.limit}, ${dto.limit}
        </if>
    </select>
</mapper>