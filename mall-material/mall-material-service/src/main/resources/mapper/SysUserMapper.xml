<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="scrbg.meplat.mall.mapper.system.SysUserMapper">


    <update id="updateUserState">
        INSERT INTO sys_user_state (user_id,state)
        VALUES (#{userId},#{state})
        ON DUPLICATE KEY UPDATE state = #{state}
    </update>
    <select id="getUserState" resultType="java.lang.Integer">
        select state from sys_user_state where user_id=#{userId}
    </select>

</mapper>