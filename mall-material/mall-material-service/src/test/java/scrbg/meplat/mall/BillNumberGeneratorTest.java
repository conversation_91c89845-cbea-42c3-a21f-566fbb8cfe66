package scrbg.meplat.mall;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import scrbg.meplat.mall.util.BillNumberGenerator;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 订单编号生成器并发测试
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("dev")
public class BillNumberGeneratorTest {

    /**
     * 测试订单编号生成的并发安全性
     */
    @Test
    public void testConcurrentOrderNumberGeneration() throws InterruptedException {
        int threadCount = 10; // 并发线程数
        int numbersPerThread = 5; // 每个线程生成的编号数量
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        
        Set<String> generatedNumbers = new HashSet<>();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failCount = new AtomicInteger(0);
        
        // 启动多个线程并发生成订单编号
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < numbersPerThread; j++) {
                        try {
                            // 生成零星采购订单编号
                            String orderNumber = BillNumberGenerator.generateOrderNumber(0, false);
                            
                            synchronized (generatedNumbers) {
                                if (generatedNumbers.contains(orderNumber)) {
                                    log.error("发现重复订单编号: {} (线程: {}, 序号: {})", orderNumber, threadId, j);
                                    failCount.incrementAndGet();
                                } else {
                                    generatedNumbers.add(orderNumber);
                                    successCount.incrementAndGet();
                                    log.info("生成订单编号: {} (线程: {}, 序号: {})", orderNumber, threadId, j);
                                }
                            }
                            
                            // 稍微延迟，增加并发冲突概率
                            Thread.sleep(10);
                            
                        } catch (Exception e) {
                            log.error("生成订单编号失败 (线程: {}, 序号: {}): {}", threadId, j, e.getMessage());
                            failCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        latch.await();
        executor.shutdown();
        
        // 输出测试结果
        log.info("=== 并发测试结果 ===");
        log.info("总线程数: {}", threadCount);
        log.info("每线程生成数: {}", numbersPerThread);
        log.info("预期总数: {}", threadCount * numbersPerThread);
        log.info("成功生成数: {}", successCount.get());
        log.info("失败数: {}", failCount.get());
        log.info("实际生成的唯一编号数: {}", generatedNumbers.size());
        log.info("是否存在重复: {}", generatedNumbers.size() != successCount.get());
        
        // 断言：不应该有重复的编号
        assert generatedNumbers.size() == successCount.get() : "存在重复的订单编号！";
        
        // 输出部分生成的编号作为示例
        log.info("=== 生成的编号示例 ===");
        generatedNumbers.stream().limit(10).forEach(number -> log.info("订单编号: {}", number));
    }

    /**
     * 测试不同类型编号的生成
     */
    @Test
    public void testDifferentNumberTypes() {
        try {
            // 测试计划编号生成
            String planNumber = BillNumberGenerator.generatePlanNumber(0);
            log.info("零星采购计划编号: {}", planNumber);
            
            String planNumber2 = BillNumberGenerator.generatePlanNumber(1);
            log.info("大宗临购计划编号: {}", planNumber2);
            
            // 测试订单编号生成
            String orderNumber = BillNumberGenerator.generateOrderNumber(0, false);
            log.info("零星采购订单编号: {}", orderNumber);
            
            String orderNumber2 = BillNumberGenerator.generateOrderNumber(1, true);
            log.info("大宗临购二级供应商订单编号: {}", orderNumber2);
            
            // 测试发货单编号生成
            String deliveryNumber = BillNumberGenerator.generateDeliveryNumber(0, false);
            log.info("零星采购发货单编号: {}", deliveryNumber);
            
            // 测试大宗临购清单编号生成
            String bulkNumber = BillNumberGenerator.generateBulkPurchaseListNumber();
            log.info("大宗临购清单编号: {}", bulkNumber);
            
        } catch (Exception e) {
            log.error("生成编号时发生异常", e);
            throw e;
        }
    }
}
